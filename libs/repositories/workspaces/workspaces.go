package workspaces

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	Create(tx *gorm.DB, input CreateInput) (model.Workspace, error)
	Update(ctx context.Context, workspaceID string, workspace UpdateInput) error
	GetMany(ctx context.Context, input GetManyInput) (GetManyOutput, error)
	GetByID(ctx context.Context, id string) (model.Workspace, error)
	GetByChannelID(ctx context.Context, channelID string) (model.Workspace, error)
}

type CreateInput struct {
	ID          string
	Name        string
	Type        model.WorkspaceTypeEnum
	Description *string
	IconURL     *string
}

type UpdateInput struct {
	Name        string
	Description *string
	IconURL     *string
}

type GetManyInput struct {
	UserID uuid.UUID
	Page   int
	Limit  int
}

type GetManyOutput struct {
	Items []model.Workspace
	Total int64
}
