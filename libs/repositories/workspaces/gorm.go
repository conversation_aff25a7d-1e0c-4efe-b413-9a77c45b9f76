package workspaces

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces/model"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{db}
}

func (g *gormRepository) Create(db *gorm.DB, input CreateInput) (model.Workspace, error) {
	var result model.Workspace
	query := `
		INSERT INTO workspaces (id, name, description, type, icon_url)
		VALUES (?, ?, ?, ?, ?)
		RETURNING id, name, description, type, icon_url, created_at
	`
	if err := db.Raw(
		query,
		input.ID,
		input.Name,
		input.Description,
		input.Type,
		input.IconURL,
	).Scan(&result).Error; err != nil {
		return model.Nil, err
	}
	return result, nil
}

func (g *gormRepository) Update(ctx context.Context, workspaceID string, workspace UpdateInput) error {
	return g.db.WithContext(ctx).Where("id = ?", workspaceID).Updates(workspace).Error
}

func (g *gormRepository) GetMany(ctx context.Context, input GetManyInput) (GetManyOutput, error) {
	var workspaces []model.Workspace

	offset := (input.Page - 1) * input.Limit

	query := g.db.WithContext(ctx).Table("workspaces").
		Joins("JOIN workspaces_members ON workspaces.id = workspaces_members.workspace_id").
		Where("workspaces_members.user_id = ?", input.UserID).
		Order("workspaces.created_at DESC").
		Limit(input.Limit).
		Offset(offset)

	if err := query.Scan(&workspaces).Error; err != nil {
		return GetManyOutput{}, err
	}

	return GetManyOutput{
		Items: workspaces,
		Total: query.RowsAffected,
	}, nil
}

func (g *gormRepository) GetByID(ctx context.Context, channelID string) (model.Workspace, error) {
	var workspace model.Workspace
	err := g.db.WithContext(ctx).Where("id = ?", channelID).First(workspace).Error
	return workspace, err
}

func (g *gormRepository) GetByChannelID(ctx context.Context, channelID string) (model.Workspace, error) {
	var workspace model.Workspace
	err := g.db.WithContext(ctx).Table("workspaces").
		Joins("JOIN channels ON workspaces.id = channels.workspace_id").
		Where("channels.id = ?", channelID).
		First(&workspace).Error
	return workspace, err
}
