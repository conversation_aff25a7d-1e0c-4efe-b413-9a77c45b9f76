package issues

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/issues/model"
	"github.com/google/uuid"
)

type Repository interface {
	GetMany(ctx context.Context, columnID string) ([]model.WorkspaceIssue, error)
	GetByID(ctx context.Context, id string) (model.WorkspaceIssue, error)
	Create(ctx context.Context, task CreateInput) (model.WorkspaceIssue, error)
	Update(ctx context.Context, id string, task UpdateInput) (model.WorkspaceIssue, error)
	Delete(ctx context.Context, id string) error
	GetByColumnID(ctx context.Context, columnID string) ([]model.WorkspaceIssue, error)
	GetByBoardID(ctx context.Context, boardID string) ([]model.WorkspaceIssue, error)
	GetByChannelID(ctx context.Context, channelID string) ([]model.WorkspaceIssue, error)
	GetBacklogByWorkspaceID(ctx context.Context, workspaceID string) ([]model.WorkspaceIssue, error)
}

type CreateInput struct {
	ID          string
	Title       string
	Description *string
	AssigneeID  *uuid.UUID
	ParentID    *string
	SprintID    *string
	WorkspaceID string
	Status      model.TaskStatusEnum
	Type        model.TaskTypeEnum
}

type UpdateInput struct {
	Title       *string
	Description *string
	AssigneeID  *uuid.UUID
	ParentID    *string
	SprintID    *string
	Status      *model.TaskStatusEnum
	Type        *model.TaskTypeEnum
}
