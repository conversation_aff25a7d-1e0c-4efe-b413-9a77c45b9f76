package issues

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/issues/model"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{
		db: db,
	}
}

func (g gormRepository) GetMany(ctx context.Context, columnID string) ([]model.WorkspaceIssue, error) {
	var result []model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("column_id = ?", columnID).Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (g gormRepository) GetByID(ctx context.Context, id string) (model.WorkspaceIssue, error) {
	var task model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("id = ?", id).First(&task).Error
	if err != nil {
		return model.Nil, err
	}
	return task, nil
}

func (g gormRepository) Create(ctx context.Context, task CreateInput) (model.WorkspaceIssue, error) {
	var result model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Create(&task).Scan(&result).Error
	if err != nil {
		return model.Nil, err
	}
	return result, nil
}

func (g gormRepository) Update(ctx context.Context, id string, task UpdateInput) (model.WorkspaceIssue, error) {
	var result model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("id = ?", id).Updates(task).Scan(
		&result,
	).Error
	if err != nil {
		return model.Nil, err
	}
	return result, nil
}

func (g gormRepository) Delete(ctx context.Context, id string) error {
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("id = ?", id).Delete(&model.WorkspaceIssue{}).Error
	if err != nil {
		return err
	}
	return nil
}

func (g gormRepository) GetByColumnID(ctx context.Context, columnID string) ([]model.WorkspaceIssue, error) {
	var issues []model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("column_id = ?", columnID).Find(&issues).Error
	if err != nil {
		return nil, err
	}
	return issues, nil
}

func (g gormRepository) GetBacklogByWorkspaceID(ctx context.Context, workspaceID string) ([]model.WorkspaceIssue, error) {
	var issues []model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("workspace_id = ?", workspaceID).Find(&issues).Error
	if err != nil {
		return nil, err
	}
	return issues, nil
}

func (g gormRepository) GetByBoardID(ctx context.Context, boardID string) ([]model.WorkspaceIssue, error) {
	var issues []model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("board_id = ?", boardID).Find(&issues).Error
	if err != nil {
		return nil, err
	}
	return issues, nil
}

func (g gormRepository) GetByChannelID(ctx context.Context, channelID string) ([]model.WorkspaceIssue, error) {
	var issues []model.WorkspaceIssue
	err := g.db.Table("workspaces_issues").WithContext(ctx).Where("channel_id = ?", channelID).Find(&issues).Error
	if err != nil {
		return nil, err
	}
	return issues, nil
}
