package model

import (
	"github.com/google/uuid"
	"time"
)

type WorkspaceTask struct {
	ID          string
	Title       string
	Description *string
	Status      TaskStatusEnum
	Type        TaskTypeEnum
	AssigneeID  *uuid.UUID
	ParentID    *string
	SprintID    *string
	WorkspaceID string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type TaskTypeEnum string

func (c TaskTypeEnum) String() string {
	return string(c)
}

const (
	TaskTypeUserStory TaskTypeEnum = "USER_STORY"
	TaskTypeTask      TaskTypeEnum = "TASK"
	TaskTypeBug       TaskTypeEnum = "BUG"
)

type TaskStatusEnum string

func (c TaskStatusEnum) String() string {
	return string(c)
}

const (
	TaskStatusTypeOpen       TaskStatusEnum = "OPEN"
	TaskStatusTypeInProgress TaskStatusEnum = "IN_PROGRESS"
	TaskStatusTypeCompleted  TaskStatusEnum = "COMPLETED"
	TaskStatusTypeCancelled  TaskStatusEnum = "CANCELLED"
)

var Nil = WorkspaceTask{}
