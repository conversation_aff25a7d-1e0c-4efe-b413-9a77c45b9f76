package boards

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/boards/model"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{
		db: db,
	}
}

func (g gormRepository) GetMany(ctx context.Context, workspaceID string) ([]model.WorkspaceBoard, error) {
	var boards []model.WorkspaceBoard
	err := g.db.WithContext(ctx).Where("workspace_id = ?", workspaceID).Find(&boards).Error
	if err != nil {
		return nil, err
	}
	return boards, nil
}

func (g gormRepository) GetByID(ctx context.Context, id string) (model.WorkspaceBoard, error) {
	var board model.WorkspaceBoard
	err := g.db.WithContext(ctx).Where("id = ?", id).First(&board).Error
	if err != nil {
		return model.Nil, err
	}
	return board, nil
}

func (g gormRepository) Create(ctx context.Context, board model.WorkspaceBoard) (model.WorkspaceBoard, error) {
	err := g.db.WithContext(ctx).Create(&board).Error
	if err != nil {
		return model.Nil, err
	}
	return board, nil
}

func (g gormRepository) Update(ctx context.Context, board model.WorkspaceBoard) (model.WorkspaceBoard, error) {
	err := g.db.WithContext(ctx).Save(&board).Error
	if err != nil {
		return model.Nil, err
	}
	return board, nil
}

func (g gormRepository) Delete(ctx context.Context, id string) error {
	err := g.db.WithContext(ctx).Where("id = ?", id).Delete(&model.WorkspaceBoard{}).Error
	if err != nil {
		return err
	}
	return nil
}

func (g gormRepository) GetByWorkspaceID(ctx context.Context, workspaceID string) ([]model.WorkspaceBoard, error) {
	var boards []model.WorkspaceBoard
	err := g.db.WithContext(ctx).Where("workspace_id = ?", workspaceID).Find(&boards).Error
	if err != nil {
		return nil, err
	}
	return boards, nil
}
