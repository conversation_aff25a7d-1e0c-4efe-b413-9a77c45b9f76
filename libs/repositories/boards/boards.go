package boards

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/boards/model"
)

type Repository interface {
	GetMany(ctx context.Context, workspaceID string) ([]model.WorkspaceBoard, error)
	GetByID(ctx context.Context, id string) (model.WorkspaceBoard, error)
	Create(ctx context.Context, board model.WorkspaceBoard) (model.WorkspaceBoard, error)
	Update(ctx context.Context, board model.WorkspaceBoard) (model.WorkspaceBoard, error)
	Delete(ctx context.Context, id string) error
	GetByWorkspaceID(ctx context.Context, WorkspaceID string) ([]model.WorkspaceBoard, error)
}
