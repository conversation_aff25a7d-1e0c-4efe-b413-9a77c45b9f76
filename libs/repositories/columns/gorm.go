package columns

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/columns/model"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{
		db: db,
	}
}

func (g gormRepository) GetMany(ctx context.Context, boardID string) ([]model.Column, error) {
	var columns []model.Column
	err := g.db.WithContext(ctx).Where("board_id = ?", boardID).Find(&columns).Error
	if err != nil {
		return nil, err
	}
	return columns, nil
}

func (g gormRepository) GetByID(ctx context.Context, id string) (model.Column, error) {
	var column model.Column
	err := g.db.WithContext(ctx).Where("id = ?", id).First(&column).Error
	if err != nil {
		return model.Nil, err
	}
	return column, nil
}

func (g gormRepository) Create(ctx context.Context, column model.Column) (model.Column, error) {
	err := g.db.WithContext(ctx).Create(&column).Error
	if err != nil {
		return model.Nil, err
	}
	return column, nil
}

func (g gormRepository) GetByBoardID(ctx context.Context, boardID string) ([]model.Column, error) {
	var columns []model.Column
	err := g.db.WithContext(ctx).Where("board_id = ?", boardID).Find(&columns).Error
	if err != nil {
		return nil, err
	}
	return columns, nil
}

func (g gormRepository) Update(ctx context.Context, column model.Column) (model.Column, error) {
	err := g.db.WithContext(ctx).Save(&column).Error
	if err != nil {
		return model.Nil, err
	}
	return column, nil
}

func (g gormRepository) Delete(ctx context.Context, id string) error {
	err := g.db.WithContext(ctx).Where("id = ?", id).Delete(&model.Column{}).Error
	if err != nil {
		return err
	}
	return nil
}
