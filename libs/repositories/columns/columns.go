package columns

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/columns/model"
)

type Repository interface {
	GetMany(ctx context.Context, boardID string) ([]model.Column, error)
	GetByID(ctx context.Context, id string) (model.Column, error)
	Create(ctx context.Context, column model.Column) (model.Column, error)
	GetByBoardID(ctx context.Context, boardID string) ([]model.Column, error)
	Update(ctx context.Context, column model.Column) (model.Column, error)
	Delete(ctx context.Context, id string) error
}
