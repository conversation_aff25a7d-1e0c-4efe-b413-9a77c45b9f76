package workspaces_members

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces_members/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type gormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &gormRepository{db}
}

func (g *gormRepository) Create(db *gorm.DB, input CreateInput) error {
	err := db.Exec(
		`
		INSERT INTO workspaces_members (workspace_id, user_id, role, invited_by, last_active)
		VALUES (?, ?, ?, ?, ?)
	`, input.WorkspaceID, input.UserID, input.Role, input.InvitedBy, input.LastActive,
	).Error

	return err
}

func (g *gormRepository) GetRole(ctx context.Context, workspaceID string, userID uuid.UUID) (model.WorkspaceMemberRole, error) {
	var role model.WorkspaceMemberRole
	err := g.db.WithContext(ctx).Table("workspaces_members").
		Where("workspace_id = ? AND user_id = ?", workspaceID, userID).
		Select("role").
		First(&role).Error

	if err != nil {
		return model.WorkspaceMemberRoleNil, err
	}

	return role, nil
}

func (g *gormRepository) GetUserIDByEmail(ctx context.Context, id string, email string) (string, error) {
	var userID string
	err := g.db.WithContext(ctx).Table("workspaces_members").
		Joins("JOIN users u ON workspaces_members.user_id = u.id").
		Where("workspace_id = ? AND u.email = ?", id, email).
		Select("u.id").
		First(&userID).Error

	if err != nil {
		return "", err
	}

	return userID, nil
}

func (g *gormRepository) GetMany(ctx context.Context, workspaceID string) ([]model.WorkspaceMember, error) {
	var users []model.WorkspaceMember
	err := g.db.WithContext(ctx).Table("workspaces_members wm").
		Select("wm.id, wm.user_id, users.name, users.avatar_url, users.email, wm.role, wm.last_active, wm.invited_by").
		Joins("JOIN users ON wm.user_id = users.id").
		Where("wm.workspace_id = ?", workspaceID).
		Find(&users).Error

	if err != nil {
		return nil, err
	}

	return users, err
}
