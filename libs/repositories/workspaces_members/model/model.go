package model

import (
	"github.com/google/uuid"
	"time"
)

type WorkspaceMember struct {
	Name        string
	AvatarURL   string
	Email       string
	WorkspaceID string
	UserID      uuid.UUID
	Role        WorkspaceMemberRole
	LastActive  time.Time
	InvitedBy   uuid.UUID
}

type WorkspaceMemberRole string

var (
	Admin  WorkspaceMemberRole = "ADMIN"
	Owner  WorkspaceMemberRole = "OWNER"
	Member WorkspaceMemberRole = "MEMBER"
)

var WorkspaceMemberNil = WorkspaceMember{}
var WorkspaceMemberRoleNil = WorkspaceMemberRole("")
