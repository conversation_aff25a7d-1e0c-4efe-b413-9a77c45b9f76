package workspaces_members

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces_members/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"time"
)

type Repository interface {
	Create(tx *gorm.DB, input CreateInput) error
	GetMany(ctx context.Context, workspaceID string) ([]model.WorkspaceMember, error)
	GetRole(ctx context.Context, workspaceID string, userID uuid.UUID) (model.WorkspaceMemberRole, error)
	GetUserIDByEmail(ctx context.Context, workspaceID string, email string) (string, error)
}

type CreateInput struct {
	WorkspaceID string
	UserID      uuid.UUID
	Role        model.WorkspaceMemberRole
	InvitedBy   uuid.UUID
	LastActive  time.Time
}
