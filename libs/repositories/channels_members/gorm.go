package channels_members

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/channels_members/model"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type GormRepository struct {
	db *gorm.DB
}

func New(db *gorm.DB) Repository {
	return &GormRepository{db}
}

func (r *GormRepository) GetMany(ctx context.Context, channelID string) ([]model.ChannelMember, error) {
	var members []model.ChannelMember
	err := r.db.WithContext(ctx).Table("channels_members").
		Select(`users.id, users.name, users.birthday, users.email, users.avatar_url, channels_members.is_muted, channels_members.last_active`).
		Joins("JOIN users ON users.id = channels_members.user_id").
		Where("channels_members.channel_id = ?", channelID).
		Scan(&members).Error
	return members, err
}

func (r *GormRepository) Create(db *gorm.DB, input CreateInput) error {
	query := `
		INSERT INTO channels_members (channel_id, user_id)
		VALUES (?, ?)
		RETURNING channel_id, user_id, is_muted, last_active
	`

	err := db.Exec(query, input.ChannelID, input.UserID).Error

	return err
}

func (r *GormRepository) Delete(channelID string, userID uuid.UUID) error {
	return r.db.Where("channel_id = ? AND user_id = ?", channelID, userID).
		Delete(&model.ChannelMember{}).Error
}

func (r *GormRepository) IsMember(channelID string, userID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.Model(&model.ChannelMember{}).
		Where("channel_id = ? AND user_id = ?", channelID, userID).
		Count(&count).Error
	return count > 0, err
}
