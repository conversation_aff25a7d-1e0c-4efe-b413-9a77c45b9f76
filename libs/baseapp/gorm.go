package baseapp

import (
	"github.com/coorpe-app/coorpe/libs/config"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
	"log"
	"os"
	"time"
)

func NewGorm(config config.Config) (*gorm.DB, error) {
	newLogger := gormlogger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		gormlogger.Config{
			SlowThreshold:             100 * time.Millisecond,
			LogLevel:                  gormlogger.Error,
			IgnoreRecordNotFoundError: true,
			ParameterizedQueries:      true,
			Colorful:                  true,
		},
	)

	db, err := gorm.Open(
		postgres.Open(config.PostgresURL), &gorm.Config{
			Logger: newLogger,
		},
	)

	db = db.Debug()

	if err != nil {
		return nil, err
	}

	cfg, _ := db.DB()
	cfg.SetMaxIdleConns(1)
	cfg.SetMaxOpenConns(10)
	cfg.SetConnMaxLifetime(time.Hour)

	return db, nil
}
