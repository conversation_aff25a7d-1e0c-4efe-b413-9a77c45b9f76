<script lang="ts" setup>
import {useWindowScroll} from '@vueuse/core'
import {useRouteQuery} from '@vueuse/router'
import {type Component, onBeforeMount, ref, watch} from 'vue'
import {useRouter} from 'vue-router'
import {TabsRoot} from 'reka-ui'
import {useTheme} from '@/composables/use-theme.js'
import {TabsContent, TabsList, TabsTrigger} from "@/components/ui/tabs";

const props = withDefaults(defineProps<PageLayoutProps>(), {
  activeTab: '',
  tabs: () => [],
  stickyHeader: false,
  noContainer: false,
})
const router = useRouter()
const {theme} = useTheme()

export interface PageLayoutProps {
  activeTab?: string
  tabs?: PageLayoutTab[]
  stickyHeader?: boolean
  cleanBody?: boolean
}

export interface PageLayoutTab {
  name: string
  title: string
  component: Component | null
  disabled?: boolean
}

const activeTab = ref(props.activeTab)

const queryActiveTab = useRouteQuery<string>('tab')
const unsubscribe = watch(queryActiveTab, setTab)
if (!props.activeTab) unsubscribe()

onBeforeMount(() => {
  if (!props.activeTab) return
  setTab()
  onChangeTab(activeTab.value, true)
})

function setTab(): void {
  const tabValue = (queryActiveTab.value ?? props.activeTab).toLowerCase()
  if (props.tabs.some((tab) => tab.name === tabValue)) {
    activeTab.value = tabValue
  }
}

function onChangeTab(tab: string | number, replace = false): void {
  router.push({query: {tab}, replace})
}

const {y} = useWindowScroll()

const shrink = ref(false)

watch(y, (value) => {
  shrink.value = value > 20;
})

</script>

<template>
  <TabsRoot v-model="activeTab" class="h-full" @update:model-value="onChangeTab">
    <div
      class='after:inset-0 p-4 px-6 min-h-[73px] flex items-center w-full border-b'
    >
      <div
        :class="{'h-20 !py-4': shrink && props.stickyHeader}"
        class="flex gap-2 items-center justify-between w-full"
      >
        <div class="flex justify-between w-full items-center gap-6 flex-wrap overflow-hidden">
          <div class="flex gap-4 items-center">
            <h1>
              <slot :activeTab="activeTab" name="title"/>
            </h1>
            <span
              class="text-sm text-muted-foreground hidden max-w-[400px] md:block">
              <slot name="description"/>
            </span>
          </div>

          <TabsList class="flex flex-wrap overflow-x-auto -mb-px" v-if="!!props.tabs.length">
            <TabsTrigger
              v-for="tab of props.tabs"
              :key="tab.name"
              :class="[
								theme === 'dark'
									? 'data-[state=active]:after:border-white'
									: 'data-[state=active]:after:border-zinc-800',
							]"
              :disabled="tab.disabled"
              :value="tab.name"
              class="tabs-trigger data-[disabled]:cursor-not-allowed data-[disabled]:text-zinc-400"
            >
              {{ tab.title }}
            </TabsTrigger>
          </TabsList>
        </div>

        <div v-if="props.tabs" class="flex gap-2 items-center">
          <slot :activeTab="activeTab" name="action"/>
        </div>
      </div>
    </div>

    <div>
      <template v-if="activeTab">
        <TabsContent v-for="tab of props.tabs" :key="tab.name" :value="tab.name">
          <component :is="tab.component" :activeTab="activeTab"/>
        </TabsContent>
      </template>

      <template v-else>
        <slot name="content"/>
      </template>
    </div>
  </TabsRoot>
</template>

<style scoped>
@reference "tailwindcss"

.tabs-trigger {
  @apply relative z-[1] flex whitespace-nowrap px-3 py-4 text-sm transition-colors before:absolute before:left-0 before:top-2 before:-z-[1] before:block before:h-9 before:w-full before:rounded-md before:transition-colors before:content-[''] hover:text-white hover:before:bg-zinc-800 data-[state=active]:after:absolute data-[state=active]:after:bottom-0 data-[state=active]:after:left-2 data-[state=active]:after:right-2 data-[state=active]:after:block data-[state=active]:after:h-0 data-[state=active]:after:border-b-2 data-[state=active]:after:content-[''] data-[state=active]:after:rounded-t-sm font-medium;
}
</style>
