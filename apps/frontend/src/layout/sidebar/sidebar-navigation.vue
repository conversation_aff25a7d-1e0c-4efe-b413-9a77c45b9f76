<script lang="ts" setup>
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from "@/components/ui/sidebar";
import {ChartNoAxesGanttIcon, Columns4Icon, GlobeIcon, Rows4Icon} from "lucide-vue-next";
import {computed} from "vue";
import Channels from "@/features/channels/channels.vue";

const links = computed(() => {
  return [
    {
      label: "Resumo",
      href: "/#",
      icon: GlobeIcon,
      disabled: true
    },
    {
      label: "Cronograma",
      href: "/#",
      icon: ChartNoAxesGanttIcon,
      disabled: true
    }, {
      label: "Backlog",
      href: "/#",
      icon: Rows4Icon,
    },
    {
      label: "Sprints Ativos",
      href: "/#",
      icon: Columns4Icon,
      disabled: true
    }
  ]
})

</script>

<template>
  <SidebarGroup class="h-full">
    <SidebarMenu class="flex justify-between h-full">
      <div>
        <Channels/>
      </div>

      <div>
        <SidebarGroupLabel>Planning</SidebarGroupLabel>
        <SidebarMenuItem v-for="link in links" :key="link.label">
          <SidebarMenuButton
            :as="'button'"
            :class="link.disabled && 'opacity-50 pointer-events-none'"
            :is-active="link.href === $route.path"
            as-child
          >
            <RouterLink :to="link.href!">
              <component :is="link.icon"/>
              <span>{{ link.label }}</span>
            </RouterLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </div>
    </SidebarMenu>
  </SidebarGroup>
</template>

