<script lang="ts" setup>
import {Sidebar, SidebarContent, SidebarInset, SidebarProvider} from "@/components/ui/sidebar";
import SidebarHeader from "./sidebar-header.vue";
import SidebarNavigation from "./sidebar-navigation.vue";
import SidebarFooter from "./sidebar-footer.vue";
import SidebarRail from "./sidebar-rail.vue";
import {useLocalStorage} from "@vueuse/core";
import {useRoute} from "vue-router";

const open = useLocalStorage('coorpeSidebarIsOpen', true)
const route = useRoute()

</script>

<template>
  <SidebarProvider class="min-h-full h-[calc(100vh-3.05rem)]" v-model:open="open"
                   style="--sidebar-width: 14rem; --sidebar-width-mobile: 14rem;"
  >
    <SidebarRail/>
    <Sidebar
      class="left-[65px] top-[49px] h-auto transition duration-0"
      collapsible="icon"
      v-if="route.meta?.showSidebar"
    >
      <SidebarHeader/>

      <SidebarContent>
        <SidebarNavigation/>
      </SidebarContent>

      <SidebarFooter/>
    </Sidebar>
    <SidebarInset>
      <slot/>
    </SidebarInset>
  </SidebarProvider>
</template>
