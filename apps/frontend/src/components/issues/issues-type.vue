<script setup lang="ts">
import {IssueTypeEnum} from "@/gql/graphql.ts";
import {computed} from "vue";
import {BookIcon, BugIcon, CheckIcon, LucideIcon} from "lucide-vue-next";

const props = defineProps<{
  issueId: string
  type: IssueTypeEnum
}>()

const typeMap = computed<Record<IssueTypeEnum, { Icon: LucideIcon }>>(() => {
  return {
    [IssueTypeEnum.Bug]: {
      Icon: BugIcon,
    },
    [IssueTypeEnum.Task]: {
      Icon: CheckIcon,
    },
    [IssueTypeEnum.UserStory]: {
      Icon: BookIcon,
    },
  }
})

const type = computed(() => typeMap.value[props.type])

</script>

<template>
  <component :is="type.Icon" :size="16"/>
</template>

