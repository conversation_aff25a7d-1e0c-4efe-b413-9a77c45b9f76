<script setup lang="ts">
import {ref} from 'vue'
import {onClickOutside} from '@vueuse/core'
import {FormControl, FormField, FormItem} from "@/components/ui/form"
import {useForm} from "vee-validate"
import {toTypedSchema} from "@vee-validate/zod"
import {z} from "zod"
import {useIssuesApi} from "@/api/issues.ts"
import {Button} from "@/components/ui/button"
import {IssueStatusEnum, IssueTypeEnum} from "@/gql/graphql.ts"
import {useRouteParams} from "@vueuse/router"
import {Input} from "@/components/ui/input"
import {PlusIcon} from "lucide-vue-next"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

const workspaceId = useRouteParams("workspaceId", String)
const showForm = ref(false)
const formRef = ref<HTMLFormElement | null>(null)

const formSchema = toTypedSchema(z.object({
  title: z.string(),
  workspaceId: z.string(),
  description: z.string().nullish(),
  assigneeId: z.string().nullish(),
  parentId: z.string().nullish(),
  sprintId: z.string().nullish(),
  status: z.nativeEnum(IssueStatusEnum).default(IssueStatusEnum.Open),
  type: z.nativeEnum(IssueTypeEnum).default(IssueTypeEnum.Task),
}))

const form = useForm({
  validationSchema: formSchema,
  validateOnMount: false,
  keepValuesOnUnmount: true,
  initialValues: {
    title: '',
    description: '',
    assigneeId: null,
    parentId: null,
    sprintId: null,
    workspaceId: workspaceId.value,
    status: IssueStatusEnum.Open,
    type: IssueTypeEnum.Task,
  }
})

const manager = useIssuesApi()
const {executeMutation, fetching} = manager.useIssuesCreateMutation()

const handleSubmit = form.handleSubmit(async (values) => {
  if (!values.title) return

  try {
    await executeMutation({input: values})
    form.resetForm()
    showForm.value = false
  } catch (error) {
    console.error(error)
  }
})

function closeForm() {
  if (!form.values.title) {
    showForm.value = false
  }
}

function openForm() {
  showForm.value = true
}

onClickOutside(formRef, closeForm)

</script>

<template>
  <div class="w-full">
    <Button
      v-if="!showForm"
      variant="ghost"
      class="w-full justify-start"
      @click="openForm"
    >
      <PlusIcon class="h-4 w-4"/>
      <span>Criar</span>
    </Button>

    <form
      v-else
      ref="formRef"
      @submit.prevent="handleSubmit"
      class="w-full"
    >
      <div class="flex gap-2 w-full">
        <FormField v-slot="{ value, handleChange }" name="type">
          <FormItem>
            <Select :model-value="value" @update:model-value="handleChange" :disabled="fetching">
              <SelectTrigger>
                <SelectValue placeholder="Tipo de tarefa"/>
              </SelectTrigger>
              <SelectContent>
                <SelectItem :value="IssueTypeEnum.Task">Tarefa</SelectItem>
                <SelectItem :value="IssueTypeEnum.Bug">Bug</SelectItem>
                <SelectItem :value="IssueTypeEnum.UserStory">História de usuário</SelectItem>
              </SelectContent>
            </Select>
          </FormItem>
        </FormField>
        <FormField v-slot="{ componentField }" name="title">
          <FormItem class="space-y-1 w-full">
            <FormControl>
              <Input
                placeholder="O que precisa ser feito?"
                v-bind="componentField"
                @blur="closeForm"
                :disabled="fetching"
              />
            </FormControl>
          </FormItem>
        </FormField>
      </div>
    </form>
  </div>
</template>
