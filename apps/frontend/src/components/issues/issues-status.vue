<script setup lang="ts">
import {IssueStatusEnum} from "@/gql/graphql.ts";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {FormField, FormItem} from "@/components/ui/form";
import {useIssuesApi} from "@/api/issues.ts";
import {toTypedSchema} from "@vee-validate/zod";
import {z} from "zod";
import {useForm} from "vee-validate";
import {watch} from "vue";

const props = defineProps<{
  issueId: string
  status: IssueStatusEnum
}>()

const formSchema = toTypedSchema(z.object({
  status: z.nativeEnum(IssueStatusEnum),
}))

const form = useForm({
  validationSchema: formSchema,
  initialValues: {
    status: props.status,
  },
})

const manager = useIssuesApi()
const {executeMutation, fetching} = manager.useIssuesUpdateMutation()

const statusMap = {
  [IssueStatusEnum.Open]: 'Aberto',
  [IssueStatusEnum.InProgress]: 'Em progresso',
  [IssueStatusEnum.Completed]: 'Concluído',
  [IssueStatusEnum.Closed]: 'Fechado',
}

watch(form.values, (newValues) => {
  if (newValues.status !== props.status) {
    try {
      executeMutation({
        id: props.issueId,
        input: {
          status: newValues.status,
        },
      })
    } catch (error) {
      console.error(error)
    }
  }
})

</script>

<template>
  <form>
    <FormField v-slot="{ value, handleChange }" name="status">
      <FormItem>
        <Select :model-value="value" @update:model-value="handleChange"
                :default-value="props.status" :disabled="fetching">
          <SelectTrigger size="sm">
            <SelectValue class="min-w-[100px]"/>
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem :value="status" v-for="status in Object.values(IssueStatusEnum)"
                          :key="status">
                {{ statusMap[status] }}
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </FormItem>
    </FormField>
  </form>
</template>

