<script setup lang="ts">
import {toTypedSchema} from '@vee-validate/zod'
import {useForm} from 'vee-validate'
import {computed} from 'vue'
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar'

import * as z from 'zod'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {FormControl, FormField, FormItem,} from '@/components/ui/form'
import {Popover, PopoverContent, PopoverTrigger,} from '@/components/ui/popover'
import {UserIcon} from "lucide-vue-next";

const props = defineProps<{
  issueId: string
  assigneeId: string | null
}>()

const assignees = [
  {label: 'English', value: 'en'},
  {label: 'French', value: 'fr'},
  {label: 'German', value: 'de'},
  {label: 'Spanish', value: 'es'},
  {label: 'Portuguese', value: 'pt'},
  {label: 'Russian', value: 'ru'},
  {label: 'Japanese', value: 'ja'},
  {label: 'Korean', value: 'ko'},
  {label: 'Chinese', value: 'zh'},
] as const

const formSchema = toTypedSchema(z.object({
  assigneeId: z.string(),
}))

const form = useForm({
  validationSchema: formSchema,
  initialValues: {
    assigneeId: '',
  },
})

const placeholder = computed(() => {
  if (!props.assigneeId) return "Não atribuído"

  return assignees.find(a => a.value === props.assigneeId)?.label ?? undefined
})

</script>

<template>
  <form>
    <FormField v-slot="{ value, handleChange }" name="assignee">
      <FormItem class="flex flex-col">
        <Popover>
          <PopoverTrigger as-child class="cursor-pointer">
            <FormControl>
              <Avatar>
                <AvatarImage src="" alt="@unovue"/>
                <AvatarFallback>
                  <UserIcon class="h-4 w-4"/>
                </AvatarFallback>
              </Avatar>
            </FormControl>
          </PopoverTrigger>
          <PopoverContent class="w-[250px] p-0" side="left">
            <Command :model-value="value" @update:model-value="handleChange">
              <CommandInput :placeholder="placeholder"/>
              <CommandEmpty>Nenhum responsável encontrado.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  <CommandItem
                    v-for="assignee in assignees"
                    :key="assignee.value"
                    :value="assignee.label"
                    @select="() => {
                      form.setFieldValue('assigneeId', assignee.value)
                    }"
                    class="py-1"
                  >
                    <Avatar>
                      <AvatarImage src="" alt="@unovue"/>
                      <AvatarFallback>
                        <UserIcon class="h-4 w-4"/>
                      </AvatarFallback>
                    </Avatar>
                    {{ assignee.label }}
                  </CommandItem>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </FormItem>
    </FormField>
  </form>
</template>
