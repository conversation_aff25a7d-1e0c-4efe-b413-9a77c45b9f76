<script setup lang="ts" generic="T extends RowData">
import {FlexRender, type RowData, type Table} from '@tanstack/vue-table'
import {
  Table as TableRoot,
  TableBody,
  TableCell,
  TableEmpty,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

defineProps<{
  table: Table<T>
  isLoading: boolean
  hideHeader?: boolean
}>()

</script>

<template>
  <slot name="caption"/>
  <div class="border border-muted rounded-md">
    <TableRoot>
      <TableHeader v-if="!hideHeader">
        <TableRow v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id"
        >
          <TableHead v-for="header in headerGroup.headers" :key="header.id"
                     :style="{ width: `${header.getSize()}%` }" class="px-5">
            <FlexRender
              v-if="!header.isPlaceholder"
              :render="header.column.columnDef.header"
              :props="header.getContext()"
            />
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <template v-if="table.getRowModel().rows?.length">
          <template v-for="row in table.getRowModel().rows" :key="row.id">
            <TableRow :data-state="row.getIsSelected() && 'selected'">
              <TableCell v-for="cell in row.getVisibleCells()" :key="cell.id" class="px-5 py-0.5">
                <FlexRender :render="cell.column.columnDef.cell" :props="cell.getContext()"/>
              </TableCell>
            </TableRow>
            <TableRow v-if="row.getIsExpanded()">
              <TableCell :colspan="row.getAllCells().length">
                {{ JSON.stringify(row.original) }}
              </TableCell>
            </TableRow>
          </template>
        </template>
        <template v-else>
          <TableEmpty>
            <slot name="empty"/>
          </TableEmpty>
        </template>
      </TableBody>
      <TableFooter class="bg-transparent">
        <TableRow class="hover:bg-transparent">
          <TableCell :colspan="table.getAllColumns().length">
            <slot name="footer"/>
          </TableCell>
        </TableRow>
      </TableFooter>
    </TableRoot>
  </div>
</template>
