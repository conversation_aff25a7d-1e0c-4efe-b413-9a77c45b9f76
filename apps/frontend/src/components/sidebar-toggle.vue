<script setup lang="ts">

import {PanelLeftClose, PanelRightClose} from "lucide-vue-next";
import {But<PERSON>} from "@/components/ui/button";
import {useLocalStorage} from "@vueuse/core";

const open = useLocalStorage("coorpeSidebarIsOpen", true);

</script>

<template>
  <div class="flex items-center gap-2">
    <Button
      variant="ghost"
      size="icon"
      @click="open = !open"
    >
      <PanelLeftClose v-if="open"/>
      <PanelRightClose v-else/>
    </Button>
  </div>
</template>
