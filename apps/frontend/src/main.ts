import "@/plugins/zod.ts";
import "./assets/index.css";
import "./main.css";

import {QueryClient, VueQueryPlugin} from "@tanstack/vue-query";
import {createApp} from "vue";
import App from "./App.vue";
import {NewRouter} from "@/plugins/router";
import urql from '@urql/vue';
import {urqlClient} from "@/plugins/urql.ts";

const app = createApp(App);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      retry: false,
    },
  },
});

VueQueryPlugin.install(app, {
  queryClient,
});

app.use(urql, urqlClient);

app.use(NewRouter()).mount("#app");

if (import.meta.env.DEV) {
  document.title = "Coorpe (dev)";
}
