<script setup lang="ts">
import PageLayout from "@/layout/page-layout.vue";
import {Ellipsis} from "lucide-vue-next"
import Backlog from "@/features/backlog/backlog.vue";
</script>

<template>
  <PageLayout clean-body>
    <template #title>
      Backlog
    </template>
    <template #description>
      Tarefas pendentes
    </template>
    <template #action>
      <Ellipsis/>
    </template>
    <template #content>
      <Backlog/>
    </template>
  </PageLayout>
</template>
