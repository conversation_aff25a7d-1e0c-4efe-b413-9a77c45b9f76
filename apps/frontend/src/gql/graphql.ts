/* eslint-disable */
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Time: { input: any; output: any; }
  UUID: { input: any; output: any; }
  Upload: { input: File; output: File; }
};

export type Board = {
  __typename?: 'Board';
  columns: Array<Column>;
  createdAt: Scalars['Time']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  ownerId: Scalars['UUID']['output'];
  title: Scalars['String']['output'];
  updatedAt: Scalars['Time']['output'];
  workspaceId: Scalars['String']['output'];
};

export type Column = {
  __typename?: 'Column';
  boardId: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  issues: Array<Issue>;
  position: Scalars['Int']['output'];
  title: Scalars['String']['output'];
};

export type CreateBoardInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  ownerId: Scalars['ID']['input'];
  title: Scalars['String']['input'];
  workspaceId: Scalars['ID']['input'];
};

export type CreateColumnInput = {
  boardId: Scalars['ID']['input'];
  position: Scalars['Int']['input'];
  title: Scalars['String']['input'];
};

export type CreateEpicInput = {
  assigneeId?: InputMaybe<Scalars['UUID']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  priority: Scalars['Int']['input'];
  status: EpicStatus;
  title: Scalars['String']['input'];
  workspaceId: Scalars['ID']['input'];
};

export type CreateIssueInput = {
  assigneeId?: InputMaybe<Scalars['UUID']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  parentId?: InputMaybe<Scalars['ID']['input']>;
  sprintId?: InputMaybe<Scalars['ID']['input']>;
  status?: IssueStatusEnum;
  title: Scalars['String']['input'];
  type?: IssueTypeEnum;
  workspaceId: Scalars['ID']['input'];
};

export type CreateSprintInput = {
  endDate: Scalars['Time']['input'];
  goal?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  startDate: Scalars['Time']['input'];
  workspaceId: Scalars['ID']['input'];
};

export type CreateStoryInput = {
  assigneeId: Scalars['UUID']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  epicId?: InputMaybe<Scalars['ID']['input']>;
  priority: Scalars['Int']['input'];
  sprintId?: InputMaybe<Scalars['ID']['input']>;
  status?: InputMaybe<StoryStatus>;
  storyPoints?: InputMaybe<Scalars['Int']['input']>;
  title: Scalars['String']['input'];
};

export type Epic = {
  __typename?: 'Epic';
  assigneeId?: Maybe<Scalars['UUID']['output']>;
  createdAt: Scalars['Time']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  priority: Scalars['Int']['output'];
  status: EpicStatus;
  stories: Array<Story>;
  title: Scalars['String']['output'];
  updatedAt: Scalars['Time']['output'];
  workspaceId: Scalars['ID']['output'];
};

export enum EpicStatus {
  Closed = 'CLOSED',
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  Open = 'OPEN'
}

export type Issue = {
  __typename?: 'Issue';
  assigneeId?: Maybe<Scalars['UUID']['output']>;
  createdAt: Scalars['Time']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  parentId?: Maybe<Scalars['ID']['output']>;
  sprintId?: Maybe<Scalars['ID']['output']>;
  status: IssueStatusEnum;
  title: Scalars['String']['output'];
  type: IssueTypeEnum;
  updatedAt: Scalars['Time']['output'];
  workspaceId: Scalars['ID']['output'];
};

export enum IssueStatusEnum {
  Closed = 'CLOSED',
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  Open = 'OPEN'
}

export enum IssueTypeEnum {
  Bug = 'BUG',
  Task = 'TASK',
  UserStory = 'USER_STORY'
}

export type Mutation = {
  __typename?: 'Mutation';
  createBoard: Board;
  createColumn: Column;
  createEpic: Epic;
  createIssue: Issue;
  createSprint: Sprint;
  createStory: Story;
  deleteBoard: Scalars['Boolean']['output'];
  deleteColumn: Scalars['Boolean']['output'];
  deleteEpic: Scalars['Boolean']['output'];
  deleteIssue: Scalars['Boolean']['output'];
  deleteSprint: Scalars['Boolean']['output'];
  deleteStory: Scalars['Boolean']['output'];
  updateBoard: Board;
  updateColumn: Column;
  updateEpic: Epic;
  updateIssue: Issue;
  updateSprint: Sprint;
  updateStory: Story;
};


export type MutationCreateBoardArgs = {
  input: CreateBoardInput;
};


export type MutationCreateColumnArgs = {
  input: CreateColumnInput;
};


export type MutationCreateEpicArgs = {
  input: CreateEpicInput;
};


export type MutationCreateIssueArgs = {
  input: CreateIssueInput;
};


export type MutationCreateSprintArgs = {
  input: CreateSprintInput;
};


export type MutationCreateStoryArgs = {
  input: CreateStoryInput;
};


export type MutationDeleteBoardArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteColumnArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteEpicArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteIssueArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSprintArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteStoryArgs = {
  id: Scalars['ID']['input'];
};


export type MutationUpdateBoardArgs = {
  input: UpdateBoardInput;
};


export type MutationUpdateColumnArgs = {
  input: UpdateColumnInput;
};


export type MutationUpdateEpicArgs = {
  input: UpdateEpicInput;
};


export type MutationUpdateIssueArgs = {
  id: Scalars['ID']['input'];
  input: UpdateIssueInput;
};


export type MutationUpdateSprintArgs = {
  input: UpdateSprintInput;
};


export type MutationUpdateStoryArgs = {
  input: UpdateStoryInput;
};

export type Query = {
  __typename?: 'Query';
  board?: Maybe<Board>;
  boards: Array<Board>;
  column?: Maybe<Column>;
  epic?: Maybe<Epic>;
  epicsByWorkspace: Array<Epic>;
  issue?: Maybe<Issue>;
  issuesBacklog: Array<Issue>;
  issuesByColumn: Array<Issue>;
  issuesBySprint: Array<Issue>;
  issuesByStory: Array<Issue>;
  sprint?: Maybe<Sprint>;
  sprintsByWorkspace: Array<Sprint>;
  storiesByEpic: Array<Story>;
  storiesBySprint: Array<Story>;
  story?: Maybe<Story>;
};


export type QueryBoardArgs = {
  id: Scalars['ID']['input'];
};


export type QueryBoardsArgs = {
  workspaceID: Scalars['ID']['input'];
};


export type QueryColumnArgs = {
  id: Scalars['ID']['input'];
};


export type QueryEpicArgs = {
  id: Scalars['ID']['input'];
};


export type QueryEpicsByWorkspaceArgs = {
  workspaceId: Scalars['ID']['input'];
};


export type QueryIssueArgs = {
  id: Scalars['ID']['input'];
};


export type QueryIssuesBacklogArgs = {
  workspaceId: Scalars['ID']['input'];
};


export type QueryIssuesByColumnArgs = {
  columnId: Scalars['ID']['input'];
};


export type QueryIssuesBySprintArgs = {
  sprintId: Scalars['ID']['input'];
};


export type QueryIssuesByStoryArgs = {
  parentId: Scalars['ID']['input'];
};


export type QuerySprintArgs = {
  id: Scalars['ID']['input'];
};


export type QuerySprintsByWorkspaceArgs = {
  workspaceId: Scalars['ID']['input'];
};


export type QueryStoriesByEpicArgs = {
  epicId: Scalars['ID']['input'];
};


export type QueryStoriesBySprintArgs = {
  sprintId: Scalars['ID']['input'];
};


export type QueryStoryArgs = {
  id: Scalars['ID']['input'];
};

export type Sprint = {
  __typename?: 'Sprint';
  boards: Array<Board>;
  createdAt: Scalars['Time']['output'];
  endDate: Scalars['Time']['output'];
  goal?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  name: Scalars['String']['output'];
  startDate: Scalars['Time']['output'];
  stories: Array<Story>;
  updatedAt: Scalars['Time']['output'];
  workspaceId: Scalars['ID']['output'];
};

export type Story = {
  __typename?: 'Story';
  assigneeId?: Maybe<Scalars['UUID']['output']>;
  createdAt: Scalars['Time']['output'];
  description?: Maybe<Scalars['String']['output']>;
  epicId?: Maybe<Scalars['ID']['output']>;
  id: Scalars['UUID']['output'];
  issues: Array<Issue>;
  priority: Scalars['Int']['output'];
  sprintId?: Maybe<Scalars['ID']['output']>;
  status: StoryStatus;
  storyPoints?: Maybe<Scalars['Int']['output']>;
  title: Scalars['String']['output'];
  updatedAt: Scalars['Time']['output'];
};

export enum StoryStatus {
  Done = 'DONE',
  InProgress = 'IN_PROGRESS',
  InReview = 'IN_REVIEW',
  ToDo = 'TO_DO'
}

export type Subscription = {
  __typename?: 'Subscription';
  boardUpdated: Board;
  columnUpdated: Column;
  epicUpdated: Epic;
  issueUpdated: Issue;
  sprintUpdated: Sprint;
  storyUpdated: Story;
};


export type SubscriptionBoardUpdatedArgs = {
  id: Scalars['ID']['input'];
};


export type SubscriptionColumnUpdatedArgs = {
  boardId: Scalars['ID']['input'];
};


export type SubscriptionEpicUpdatedArgs = {
  boardId: Scalars['ID']['input'];
};


export type SubscriptionIssueUpdatedArgs = {
  boardId: Scalars['ID']['input'];
};


export type SubscriptionSprintUpdatedArgs = {
  workspaceId: Scalars['ID']['input'];
};


export type SubscriptionStoryUpdatedArgs = {
  sprintId: Scalars['ID']['input'];
};

export type UpdateBoardInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  title: Scalars['String']['input'];
};

export type UpdateColumnInput = {
  boardId?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  position?: InputMaybe<Scalars['Int']['input']>;
  title: Scalars['String']['input'];
};

export type UpdateEpicInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  priority?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<EpicStatus>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateIssueInput = {
  assigneeId?: InputMaybe<Scalars['UUID']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  parentId?: InputMaybe<Scalars['ID']['input']>;
  sprintId?: InputMaybe<Scalars['ID']['input']>;
  status?: InputMaybe<IssueStatusEnum>;
  title?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<IssueTypeEnum>;
};

export type UpdateSprintInput = {
  endDate?: InputMaybe<Scalars['Time']['input']>;
  goal?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  name: Scalars['String']['input'];
  startDate?: InputMaybe<Scalars['Time']['input']>;
};

export type UpdateStoryInput = {
  assigneeId?: InputMaybe<Scalars['UUID']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  epicId?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  priority?: InputMaybe<Scalars['Int']['input']>;
  sprintId?: InputMaybe<Scalars['ID']['input']>;
  status?: InputMaybe<StoryStatus>;
  storyPoints?: InputMaybe<Scalars['Int']['input']>;
  title: Scalars['String']['input'];
};

export type CreateIssueMutationVariables = Exact<{
  input: CreateIssueInput;
}>;


export type CreateIssueMutation = { __typename?: 'Mutation', createIssue: { __typename?: 'Issue', id: string, title: string, description?: string | null, status: IssueStatusEnum, assigneeId?: any | null, parentId?: string | null, sprintId?: string | null, type: IssueTypeEnum, workspaceId: string, createdAt: any, updatedAt: any } };

export type UpdateIssueMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  input: UpdateIssueInput;
}>;


export type UpdateIssueMutation = { __typename?: 'Mutation', updateIssue: { __typename?: 'Issue', title: string, description?: string | null, status: IssueStatusEnum, assigneeId?: any | null, parentId?: string | null, type: IssueTypeEnum } };

export type IssuesBacklogQueryVariables = Exact<{
  workspaceId: Scalars['ID']['input'];
}>;


export type IssuesBacklogQuery = { __typename?: 'Query', issuesBacklog: Array<{ __typename?: 'Issue', id: string, title: string, description?: string | null, status: IssueStatusEnum, assigneeId?: any | null, type: IssueTypeEnum, workspaceId: string, createdAt: any, updatedAt: any }> };


export const CreateIssueDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateIssue"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CreateIssueInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createIssue"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"assigneeId"}},{"kind":"Field","name":{"kind":"Name","value":"parentId"}},{"kind":"Field","name":{"kind":"Name","value":"sprintId"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"workspaceId"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}}]}}]} as unknown as DocumentNode<CreateIssueMutation, CreateIssueMutationVariables>;
export const UpdateIssueDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateIssue"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"UpdateIssueInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateIssue"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"assigneeId"}},{"kind":"Field","name":{"kind":"Name","value":"parentId"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}}]}}]} as unknown as DocumentNode<UpdateIssueMutation, UpdateIssueMutationVariables>;
export const IssuesBacklogDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"IssuesBacklog"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"workspaceId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"issuesBacklog"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"workspaceId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"workspaceId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"assigneeId"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"workspaceId"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}}]}}]} as unknown as DocumentNode<IssuesBacklogQuery, IssuesBacklogQueryVariables>;