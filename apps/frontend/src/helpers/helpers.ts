import {differenceInSeconds, format, isThisYear, isToday, isYesterday} from 'date-fns';
import {ptBR} from 'date-fns/locale/pt-BR';

export function removeHTMLTags(html: string): string {
  if (!html) {
    return "";
  }

  return html.replace(/<[^>]*>/g, '').trim();
}

export function getInitials(text: string) {
  return text
    .split(" ")
    .map(word => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
}

export function fdn(dateStr: string) {
  const date = new Date(dateStr);

  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string provided');
  }

  const now = new Date();
  const diffSeconds = differenceInSeconds(now, date);

  if (diffSeconds < 10) {
    return 'Agora mesmo';
  }

  if (isToday(date)) {
    return `Hoje, ${format(date, 'HH:mm', {locale: ptBR})}`;
  }

  if (isYesterday(date)) {
    return `Ontem, ${format(date, 'HH:mm', {locale: ptBR})}`;
  }

  return format(date, `dd/MM${!isThisYear(date) ? "/yyyy" : ""} HH:mm`, {locale: ptBR});
}
