import {useMutation, useQuery, useQueryClient} from "@tanstack/vue-query";
import {createGlobalState} from "@vueuse/core";
import {api} from "@/plugins/api.ts";
import {z} from "zod";
import {WorkspaceOutputDtoTypeEnum} from "@coorpe/api/api";

const invalidationKey = "workspacesInvalidationKey";

export const updateWorkspaceSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().nullish(),
  icon: z.instanceof(File).optional(),
  type: z.nativeEnum(WorkspaceOutputDtoTypeEnum),
})

export const createWorkspaceSchema = updateWorkspaceSchema.omit({id: true})

export type UpdateWorkspaceInput = z.infer<typeof updateWorkspaceSchema>
export type CreateWorkspaceInput = z.infer<typeof createWorkspaceSchema>

export const useWorkspacesApi = createGlobalState(() => {
  const queryClient = useQueryClient();

  const useQueryWorkspaces = () => useQuery({
    queryFn: async () => {
      const response = await api.getAllWorkspaces()
      return response.data;
    },
    queryKey: [invalidationKey],
  });

  const useQueryWorkspace = (id: string) => useQuery({
    queryFn: async () => {
      const response = await api.getWorkspaceById(id)
      return response.data;
    },
    enabled: !!id,
    queryKey: [invalidationKey, id],
  });

  const useMutationCreateWorkspace = () => useMutation({
    mutationFn: async (data: CreateWorkspaceInput) => {
      const response = await api.createWorkspace(data)
      return response.data;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({queryKey: [invalidationKey]});
    },
    mutationKey: [invalidationKey],
  });

  const useMutationUpdateWorkspace = () => useMutation({
    mutationFn: async (data: UpdateWorkspaceInput) => {
      const response = await api.updateWorkspace(data.id, {
        name: data.name,
        description: data.description,
      })
      return response.data;
    },
    async onSuccess(data) {
      await queryClient.invalidateQueries({queryKey: [invalidationKey, data.id]});
    },
    mutationKey: [invalidationKey],
  });

  return {
    useQueryWorkspaces,
    useQueryWorkspace,

    useMutationCreateWorkspace,
    useMutationUpdateWorkspace,
  };
});
