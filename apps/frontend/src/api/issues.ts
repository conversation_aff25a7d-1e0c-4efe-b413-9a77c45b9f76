import {createGlobalState} from "@vueuse/core";
import {useQuery} from '@urql/vue'
import {graphql} from "@/gql/gql.ts";
import {MaybeRef, unref} from "vue";
import type {IssuesBacklogQuery} from '@/gql/graphql.js'
import {useMutation} from "@/composables/use-mutation.ts";

const invalidationKey = 'issuesInvalidationKey';

export type IssuesBacklog = Omit<IssuesBacklogQuery["issuesBacklog"][number], "__typename">

export const useIssuesApi = createGlobalState(() => {
  const useIssuesCreateMutation = () => useMutation(graphql(`
    mutation CreateIssue($input: CreateIssueInput!) {
      createIssue(input: $input) {
        id
        title
        description
        status
        assigneeId
        parentId
        sprintId
        type
        workspaceId
        createdAt
        updatedAt
      }
    }
  `), [invalidationKey])

  const useIssuesUpdateMutation = () => useMutation(graphql(`
    mutation UpdateIssue($id: ID!, $input: UpdateIssueInput!) {
      updateIssue(id: $id, input: $input) {
        title
        description
        status
        assigneeId
        parentId
        type
      }
    }
  `), [invalidationKey])

  const useIssuesBacklog = (workspaceId: MaybeRef<string | null>) => {
    const id = unref(workspaceId)

    return useQuery({
      context: {additionalTypenames: [invalidationKey]},
      query: graphql(`
        query IssuesBacklog($workspaceId: ID!) {
          issuesBacklog(workspaceId: $workspaceId) {
            id
            title
            description
            status
            assigneeId
            type
            workspaceId
            createdAt
            updatedAt
          }
        }
      `),
      get variables() {
        return {workspaceId: id!}
      },
      pause: !id,
    })
  }

  return {
    useIssuesCreateMutation,
    useIssuesUpdateMutation,
    useIssuesBacklog,
  }
})

