import {api} from "@/plugins/api.ts";
import {useMutation, useQuery, useQueryClient} from "@tanstack/vue-query";
import {toTypedSchema} from "@vee-validate/zod";
import {createGlobalState} from "@vueuse/core";
import {computed, reactive} from "vue";
import {useRouter} from "vue-router";
import {z} from "zod";
import {LoginInputDto, MeOutputDto, RegisterInputDto} from "@coorpe/api/api";

const invalidationKey = "authInvalidationKey";

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(2, "Senha deve ter pelo menos 8 caracteres"),
});

export const registerSchema = z.object({
  name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  email: z.string().email(),
  password: z
    .string()
    .min(8, "Senha deve ter pelo menos 8 caracteres")
    .max(128, "Senha deve ter no máximo 100 caracteres"),
  birthday: z.string().min(1, "Data de nascimento é obrigatória"),
});

export const loginFormSchema = toTypedSchema(loginSchema);
export const registerFormSchema = toTypedSchema(registerSchema);

export const useAuthApi = createGlobalState(() => {
  const queryClient = useQueryClient();
  const router = useRouter();

  const useMutationLogin = () => useMutation({
    mutationKey: [invalidationKey],
    mutationFn: (data: LoginInputDto) => api.login(data),
    onSuccess: async () => {
      await queryClient.invalidateQueries({queryKey: [invalidationKey]});
      await router.push("/app");
    },
  });

  const useMutationRegister = () => reactive(
    useMutation({
      mutationKey: [invalidationKey],
      mutationFn: async (data: RegisterInputDto) => api.register(data),
      onSuccess: () => router.push("/login"),
    })
  );

  const useMutationLogout = () => reactive(
    useMutation({
      mutationKey: [invalidationKey],
      mutationFn: () => api.logout(),
      onSuccess: async () => {
        await queryClient.invalidateQueries({queryKey: [invalidationKey]});
        await router.push("/");
      },
    })
  );

  return {
    useMutationLogin,
    useMutationRegister,
    useMutationLogout,
  };
});

export const useProfile = createGlobalState(() => {
  const {data, isLoading, error} = useQuery({
    queryKey: [invalidationKey],
    queryFn: async () => await api.me(),
  });

  const user = computed<MeOutputDto | null>(() => {
    const userData = data.value?.data;
    if (!userData) return null;

    return {
      id: userData.id,
      email: userData.email,
      name: userData.name,
      birthday: userData.birthday,
      emailVerified: userData.emailVerified,
      avatarURL: userData.avatarURL,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
    };
  });

  const isAuthenticated = computed(() => {
    return !!user.value && !error.value;
  });

  return {
    user,
    isLoading,
    isAuthenticated,
    error,
  };
});
