import {api} from "@/plugins/api.ts";
import {useInfiniteQuery, useMutation} from "@tanstack/vue-query";
import {useWebSocket} from "@vueuse/core";
import {z} from "zod";
import {GetMessagesOutputDto, MessageOutputDto, SendMessageInputDtoTypeEnum} from "@coorpe/api/api";
import {computed, MaybeRef, unref} from "vue";

export const sendMessageSchema = z.object({
  body: z.string().min(1, "Mensagem é obrigatória"),
  workspaceId: z.string(),
  channelId: z.string().nullable().optional(),
  conversationId: z.string().nullable().optional(),
  type: z.nativeEnum(SendMessageInputDtoTypeEnum)
});

export const getChannelMessagesInput = z.object({
  channelId: z.string(),
  limit: z.number().min(1).default(10),
  page: z.number().min(1).default(1),
});

export type GetChannelMessagesInput = z.infer<typeof getChannelMessagesInput>;
export type SendMessageInput = z.infer<typeof sendMessageSchema>;

const invalidationKey = "messagesInvalidationKey";

export const useMessages = (input: MaybeRef<GetChannelMessagesInput>) => {
  const opts = unref(input);

  return useInfiniteQuery<GetMessagesOutputDto>({
    queryKey: [invalidationKey, opts.channelId],
    queryFn: async (ctx) => {
      const response = await api.getChannelMessages(opts.channelId, {
        page: ctx.pageParam as number,
        limit: opts.limit,
      });
      return response.data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (lastPage.items.length === 0) {
        return;
      }
      return allPages.length + 1;
    },
    initialPageParam: 1,
  });
}

export const useMutationSendMessage = () =>
  useMutation({
    mutationKey: [invalidationKey],
    mutationFn: (data: SendMessageInput) => api.sendChannelMessages(data)
  });

export const useMessagesSubscription = (id: MaybeRef<string>) => {
  const {data} = useWebSocket(`ws://localhost:8080/wss/channel/${unref(id)}/messages`);

  const newMessage = computed(() => {
    try {
      return JSON.parse(data.value) as MessageOutputDto;
    } catch (e) {
      console.error('Failed to parse WebSocket messages:', e);
      return null;
    }
  });

  return {
    data: newMessage
  };
};
