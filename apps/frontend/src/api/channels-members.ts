import {createGlobalState} from "@vueuse/core";
import {api} from "@/plugins/api.ts";
import {useQuery} from "@tanstack/vue-query";
import {useChannelPopover} from "@/features/channels-members/composables/use-channel-popover.ts";
import {z} from "zod";
import {GetChannelMembersOutputDto} from "@coorpe/api/api";
import {computed} from "vue";

const invalidationKey = 'channelMembersInvalidationKey';

const getChannelMembersInput = z.object({
  channelId: z.string(),
  limit: z.number().min(1).default(50).optional(),
  page: z.number().min(1).default(1).optional(),
})

export type GetChannelMembersInput = z.infer<typeof getChannelMembersInput>

export const useChannelMembersApi = createGlobalState(() => {
  const {open} = useChannelPopover()

  const useQueryMembers = (input: GetChannelMembersInput) => useQuery<GetChannelMembersOutputDto>({
    queryKey: [invalidationKey, input.channelId],
    queryFn: async () => {
      const response = await api.getChannelMembers(input.channelId)
      return response.data;
    },
    enabled: computed(() => open.value && !!input.channelId)
  })

  return {
    useQueryMembers,
  }
})
