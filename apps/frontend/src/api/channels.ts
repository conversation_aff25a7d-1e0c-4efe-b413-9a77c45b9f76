import {createGlobalState} from "@vueuse/core";
import {useMutation, useQuery, useQueryClient} from "@tanstack/vue-query";
import {computed, MaybeRef, unref} from "vue";
import {CreateChannelOutputDto, GetChannelsOutputDto} from "@coorpe/api/api";
import {api} from "@/plugins/api.ts";

const invalidationKey = "channelsInvalidationKey";

export const useChannelApi = createGlobalState(() => {
  const queryClient = useQueryClient()

  const useQueryChannels = (workspaceId: MaybeRef<string | null>) => {
    return useQuery<GetChannelsOutputDto>({
      queryFn: async () => {
        const id = unref(workspaceId)
        const response = await api.getChannels(id!)
        return response.data;
      },
      enabled: computed(() => !!unref(workspaceId)),
      queryKey: [invalidationKey, workspaceId],
    })
  }

  const useMutationCreateChannel = () => useMutation({
    mutationFn: async (data: CreateChannelOutputDto) => {
      const response = await api.createChannel(data)
      return response.data;
    },
    async onSuccess() {
      await queryClient.invalidateQueries({queryKey: [invalidationKey]})
    },
    mutationKey: [invalidationKey],
  })

  return {
    useQueryChannels,

    useMutationCreateChannel,
  }
})
