<script lang="ts" setup>
import {Camera} from "lucide-vue-next";
import {useDropZone, useFileDialog, useObjectUrl} from "@vueuse/core";
import {shallowRef, useTemplateRef, watch} from "vue";

const props = defineProps<{
  initialUrl?: string | null
}>()

const emits = defineEmits<{
  change: [file: File | null]
}>()

const dropzoneRef = useTemplateRef<HTMLElement>('dropzoneRef')
const fileRef = shallowRef<File | null>(null)
const objectUrl = useObjectUrl(fileRef)

const {open, onChange} = useFileDialog({
  accept: 'image/*',
  multiple: false,
})

const {isOverDropZone, files} = useDropZone(dropzoneRef, {
  multiple: false,
})

onChange((files) => {
  fileRef.value = files?.[0] || null
  emits('change', fileRef.value)
})

watch(files, (files) => {
  fileRef.value = files?.[0] || null
  emits('change', fileRef.value)
})

function handleClick() {
  open()
}
</script>

<template>
  <div
    ref="dropzoneRef"
    :class="['rounded-lg cursor-pointer flex mx-auto gap-4 size-28 items-center justify-center transition-colors',
               !objectUrl && !props.initialUrl && 'border border-dashed hover:border-primary cursor-pointer',
               isOverDropZone && 'border border-dashed border-primary']"
    :style="objectUrl || props.initialUrl ? {
        backgroundImage: `url(${objectUrl || props.initialUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      } : {}"
    @click="handleClick"
  >
    <Camera v-if="!objectUrl && !props.initialUrl"/>
  </div>
</template>
