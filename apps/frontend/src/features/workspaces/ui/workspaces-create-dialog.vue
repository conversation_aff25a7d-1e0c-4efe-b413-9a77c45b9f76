<script lang='ts' setup>
import {ref} from 'vue'
import {useForm} from 'vee-validate'
import {toTypedSchema} from '@vee-validate/zod'

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON><PERSON>Trigger,
} from '@/components/ui/dialog'
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {Textarea} from '@/components/ui/textarea'
import {FormControl, FormField, FormItem, FormLabel,} from '@/components/ui/form'
import {Loader2, PlusIcon} from "lucide-vue-next";
import {useProfile} from "@/api/auth.ts";
import WorkspacesIcon from "@/features/workspaces/ui/workspaces-icon.vue";
import {useWorkspaces} from "@/features/workspaces/composables/use-workspaces.ts";
import {createWorkspaceSchema} from "@/api/workspaces.ts";
import {WorkspaceOutputDtoTypeEnum} from "@coorpe/api/api";
import ScrumIcon from "@/assets/workspaces/scrum-icon.svg?use";
import KanbanIcon from "@/assets/workspaces/kanban-icon.svg?use";
import {ToggleGroup, ToggleGroupItem} from '@/components/ui/toggle-group'

const emits = defineEmits<{
  close: []
}>()

const open = ref(false)
const imageFile = ref<File | null>(null)
const {user} = useProfile()
const {createWorkspace, viewWorkspace} = useWorkspaces()

const form = useForm({
  validationSchema: toTypedSchema(createWorkspaceSchema),
  validateOnMount: false,
  keepValuesOnUnmount: true,
  initialValues: {
    name: `Workspace de ${user?.value?.name}`,
    type: WorkspaceOutputDtoTypeEnum.SCRUM,
  }
})

const handleSubmit = form.handleSubmit(async (data) => {
  try {
    if (imageFile.value) {
      data.icon = imageFile.value
    }

    const result = await createWorkspace.mutateAsync(data)

    if (!!result) {
      await viewWorkspace(result.id)
    }
    emits('close')
    open.value = false
  } catch (e) {
    console.error(e)
  }
})

</script>

<template>
  <Dialog v-model:open="open">
    <DialogTrigger as-child>
      <Button
        class="size-12"
        size="icon"
        variant="ghost"
      >
        <PlusIcon class="size-6"/>
      </Button>
    </DialogTrigger>

    <DialogContent class="sm:max-w-[525px] sm:min-h-[calc(80vh-10rem)]">
      <DialogHeader>
        <DialogTitle>Criar workspace</DialogTitle>
      </DialogHeader>

      <WorkspacesIcon @change="file => imageFile = file"/>

      <form class="grid gap-4 py-4" @submit.prevent="handleSubmit">
        <FormField v-slot="{ componentField }" name="name">
          <FormItem>
            <FormLabel>Nome</FormLabel>
            <FormControl>
              <Input
                placeholder="Digite o nome do workspace"
                v-bind="componentField"
              />
            </FormControl>
          </FormItem>
        </FormField>

        <FormField v-slot="{ componentField }" name="description">
          <FormItem>
            <FormLabel>Descrição</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Descreva seu workspace..."
                v-bind="componentField"
              />
            </FormControl>
          </FormItem>
        </FormField>

        <FormField v-slot="{ componentField }" name="type">
          <FormItem>
            <FormLabel>Tipo (metodologia)</FormLabel>
            <FormControl>
              <ToggleGroup
                type="single"
                class="mx-auto w-full"
                variant="outline"
                v-bind="componentField"
              >
                <ToggleGroupItem value="KANBAN" class="h-full">
                  <KanbanIcon class="size-24 fill-primary"/>
                  Kanban
                </ToggleGroupItem>
                <ToggleGroupItem value="SCRUM" class="h-full">
                  <ScrumIcon class="size-24 fill-primary"/>
                  Scrum
                </ToggleGroupItem>
              </ToggleGroup>
            </FormControl>
          </FormItem>
        </FormField>

        <DialogFooter class="items-end">
          <Button :disabled="createWorkspace.isPending.value" type="submit">
            <template v-if="createWorkspace.isPending.value">
              <Loader2 class="mr-2 h-4 w-4 animate-spin"/>
              Carregando
            </template>
            <template v-else>
              Criar workspace
            </template>
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
