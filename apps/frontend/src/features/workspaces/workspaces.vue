<script setup lang="ts">

import {TooltipProvider} from "@/components/ui/tooltip";
import WorkspacesCreateDialog from "@/features/workspaces/ui/workspaces-create-dialog.vue";
import WorkspacesList from "@/features/workspaces/ui/workspaces-list.vue";
</script>

<template>
  <TooltipProvider>
    <div
      class="flex flex-col h-full bg-sidebar-accent border-r border-border py-4 px-2">
      <WorkspacesList/>
      <WorkspacesCreateDialog/>
    </div>
  </TooltipProvider>
</template>
