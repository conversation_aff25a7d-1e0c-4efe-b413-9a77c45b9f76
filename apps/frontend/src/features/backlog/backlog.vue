<script setup lang="ts">
import {useBacklogTable} from "@/features/backlog/composables/use-backlog-table.ts";
import Table from "@/components/table.vue";
import IssuesCreate from "@/components/issues/issues-create.vue";

const {table, isLoading, tableSize} = useBacklogTable()
</script>

<template>
  <div class="m-6 space-y-4">
    <Table :table="table" :is-loading="isLoading">
      <template #caption>
        Backlog ({{ tableSize }} issues)
      </template>
      <template #empty>
        Nenhuma tarefa no backlog
      </template>
      <template #footer>
        <IssuesCreate/>
      </template>
    </Table>
  </div>
</template>
