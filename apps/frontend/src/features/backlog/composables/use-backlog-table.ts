import {createGlobalState} from "@vueuse/core";
import {useBacklog} from "@/features/backlog/composables/use-backlog.ts";
import {computed, h} from "vue";
import {ColumnDef, getCoreRowModel, useVueTable} from '@tanstack/vue-table'
import {IssuesBacklog} from "@/api/issues.ts";
import {Button} from "@/components/ui/button";
import IssuesStatus from "@/components/issues/issues-status.vue";
import IssuesAssignee from "@/components/issues/issues-assignee.vue";
import IssuesType from "@/components/issues/issues-type.vue";

export const useBacklogTable = createGlobalState(() => {
  const {issuesList, issuesCount, fetching, viewIssue} = useBacklog()

  const tableColumns = computed<ColumnDef<IssuesBacklog>[]>(() => [
    {
      accessorKey: 'type',
      cell: ({row}) => h(IssuesType, {
        issueId: row.original.id,
        type: row.original.type
      }),
      size: 10,
    },
    {
      accessorKey: 'title',
      cell: ({row}) => h(Button, {
        variant: "link",
        size: "sm",
        onClick: () => viewIssue(row.original.id)
      }, row.original.title),
      size: 100,
    },
    {
      accessorKey: 'status',
      cell: ({row}) => h(IssuesStatus, {
        issueId: row.original.id,
        status: row.original.status
      }),
      size: 20,
    },
    {
      accessorKey: 'assigneeId',
      cell: ({row}) => h(IssuesAssignee, {
        issueId: row.original.id,
        assigneeId: row.original.assigneeId,
        class: "flex justify-end"
      }),
      size: 10,
    }
  ])

  const table = useVueTable({
    get data() {
      return issuesList.value
    },
    get columns() {
      return tableColumns.value
    },
    getCoreRowModel: getCoreRowModel(),
  })

  return {
    table,
    tableSize: issuesCount,
    isLoading: fetching,
  }
})
