import {createGlobalState} from "@vueuse/core";
import {IssuesBacklog, useIssuesApi} from "@/api/issues.ts";
import {useRouteParams} from "@vueuse/router";
import {computed} from "vue";
import {useRouter} from "vue-router";

export const useBacklog = createGlobalState(() => {
  const workspaceId = useRouteParams("workspaceId", String)
  const router = useRouter()

  const issuesApi = useIssuesApi()

  const {data, fetching} = issuesApi.useIssuesBacklog(workspaceId)

  const issuesList = computed<IssuesBacklog[]>(() => {
    return data.value?.issuesBacklog as IssuesBacklog[] ?? []
  })

  const issuesCount = computed(() => issuesList.value.length)

  async function viewIssue(id: string) {
    await router.push(`/app/${workspaceId.value}/t/${id}`)
  }

  return {
    issuesList,
    issuesCount,
    viewIssue,
    fetching,
  }
})
