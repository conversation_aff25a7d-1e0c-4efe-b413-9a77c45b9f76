<script setup lang="ts">

import {SidebarGroupLabel, SidebarMenuButton, SidebarMenuItem} from "@/components/ui/sidebar";
import ChannelsCreateDialog from "./ui/channels-create-dialog.vue";
import {HashIcon} from "lucide-vue-next";
import {useChannels} from "./composables/use-channels.ts";

const {channels, viewChannel, currentChannelId} = useChannels()
</script>

<template>
  <SidebarGroupLabel class="flex justify-between">
    Canais
    <ChannelsCreateDialog/>
  </SidebarGroupLabel>
  <SidebarMenuItem v-for="channel in channels" :key="channel.id" class="my-1">
    <SidebarMenuButton
      :is-active="currentChannelId === channel.id"
      as-child
      class="cursor-pointer"
      @click="viewChannel(channel.id)"
    >
      <component>
        <HashIcon/>
        <span>{{ channel.name }}</span>
      </component>
    </SidebarMenuButton>
  </SidebarMenuItem>
</template>
