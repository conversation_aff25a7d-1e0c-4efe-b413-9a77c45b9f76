import {useProfile} from "@/api/auth.ts";
import type {RouteRecordRaw} from "vue-router";
import {createRouter, createWebHistory} from "vue-router";
import {until} from "@vueuse/core";

export function NewRouter() {
  const routes: ReadonlyArray<RouteRecordRaw> = [
    {
      name: "Landing",
      path: "/",
      component: () => import("../pages/Landing.vue"),
    },
    {
      name: "Login",
      path: "/login",
      component: () => import("../pages/Login.vue"),
    },
    {
      name: "Register",
      path: "/register",
      component: () => import("../pages/Register.vue"),
    },
    {
      path: "/app",
      component: () => import("../layout/layout.vue"),
      meta: {requiresAuth: true},
      children: [
        {
          name: "App",
          path: "/app",
          component: () => import("../pages/App.vue"),
          meta: {showSidebar: false}
        },
        {
          path: "/app/:workspaceId",
          component: () => import("../pages/Backlog.vue"),
          meta: {showSidebar: true}
        },
        {
          path: "/app/:workspaceId/:channelId",
          component: () => import("../pages/Channel.vue"),
          meta: {showSidebar: true}
        },
      ],
    },
  ];

  const router = createRouter({
    history: createWebHistory(),
    routes,
  });

  router.beforeEach(async (to, _, next) => {
    try {
      const {isAuthenticated, isLoading} = useProfile();

      if (isLoading.value) {
        await until(isLoading).toBe(false);
      }

      if ((to.name === "Login" || to.name === "Register") && isAuthenticated.value) {
        return next({name: "App"});
      }

      if (to.meta.requiresAuth && !isAuthenticated.value) {
        return next({name: "Login"});
      }

      return next();
    } catch (error) {
      console.log(error)
    }
  });

  return router;
}
