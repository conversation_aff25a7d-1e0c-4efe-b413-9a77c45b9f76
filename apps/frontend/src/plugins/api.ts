import {Api, HttpClient} from "@coorpe/api/api";

export function useApi(opts?: { headers?: Record<string, any> }) {
  const apiUrl = import.meta.env.SSR
    ? process.env.NODE_ENV === "production"
      ? "http://coorpe.com/api"
      : "http://localhost:3000"
    : `${window.location.origin}`;

  let headers: Record<string, any> = {};
  try {
    if (opts?.headers) {
      headers = opts.headers;
    }
  } catch {
  }

  return new Api(
    new HttpClient({
      baseUrl: apiUrl,
      baseApiParams: {
        credentials: "include",
        headers,
      },
    })
  );
}

export const {api} = useApi();
export const {http} = useApi();
