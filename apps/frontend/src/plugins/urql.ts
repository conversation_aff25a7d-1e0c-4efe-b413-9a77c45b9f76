import {cacheExchange, Client, fetchExchange, mapExchange, subscriptionExchange} from '@urql/vue';
import {createClient as createWS, SubscribePayload} from 'graphql-ws'
import {ref} from "vue";


const wsUrl = `${window.location.protocol === 'https:' ? 'wss' : 'ws'}://${window.location.host}/query`
const gqlApiUrl = `${window.location.protocol}//${window.location.host}/query`

const gqlWs = createWS({
  url: wsUrl,
  lazy: true,
  shouldRetry: () => true,
})

function createClient() {
  return new Client({
    url: gqlApiUrl,
    exchanges: [
      mapExchange({
        onError(error, _operation) {
          for (const er of error.graphQLErrors) {
            if (er.extensions.code !== 'BAD_REQUEST') continue
            const validationErrors = Object.entries(er.extensions.validation_errors ?? {} as Record<string, any>)

            console.log(validationErrors)
          }
        },
      }),
      cacheExchange,
      fetchExchange,
      subscriptionExchange({
        enableAllOperations: true,
        forwardSubscription: (operation) => ({
          subscribe: (sink) => ({
            unsubscribe: gqlWs.subscribe(operation as SubscribePayload, sink),
          }),
        }),
      }),
    ],
    fetchOptions: () => {
      const options: RequestInit = {
        credentials: 'include',
      }

      return options
    },
  });
}

export const urqlClient = ref<Client>(createClient())
