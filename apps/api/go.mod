module github.com/coorpe-app/coorpe

go 1.24

replace (
	github.com/coorpe-app/coorpe/libs/baseapp => ../../libs/baseapp
	github.com/coorpe-app/coorpe/libs/bus => ../../libs/bus
	github.com/coorpe-app/coorpe/libs/config => ../../libs/config
	github.com/coorpe-app/coorpe/libs/logger => ../../libs/logger
)

require (
	github.com/99designs/gqlgen v0.17.74
	github.com/alexedwards/scs/goredisstore v0.0.0-20250417082927-ab20b3feb5e9
	github.com/alexedwards/scs/v2 v2.8.0
	github.com/coorpe-app/coorpe/libs/baseapp v0.0.0-00010101000000-000000000000
	github.com/coorpe-app/coorpe/libs/bus v0.0.0-00010101000000-000000000000
	github.com/coorpe-app/coorpe/libs/config v0.0.0-00010101000000-000000000000
	github.com/coorpe-app/coorpe/libs/logger v0.0.0-00010101000000-000000000000
	github.com/danielgtaylor/huma/v2 v2.32.0
	github.com/gin-contrib/cors v1.7.3
	github.com/gin-gonic/gin v1.10.0
	github.com/go-playground/locales v0.14.1
	github.com/go-playground/universal-translator v0.18.1
	github.com/go-playground/validator/v10 v10.23.0
	github.com/goccy/go-json v0.10.5
	github.com/google/uuid v1.6.0
	github.com/gorilla/websocket v1.5.3
	github.com/guregu/null v4.0.0+incompatible
	github.com/json-iterator/go v1.1.12
	github.com/matoous/go-nanoid/v2 v2.1.0
	github.com/minio/minio-go/v7 v7.0.88
	github.com/nats-io/nats.go v1.42.0
	github.com/redis/go-redis/v9 v9.7.1
	github.com/samber/lo v1.49.1
	github.com/vektah/gqlparser/v2 v2.5.27
	go.uber.org/fx v1.24.0
	go.uber.org/zap v1.26.0
	golang.org/x/crypto v0.38.0
	golang.org/x/exp v0.0.0-20250506013437-ce4c2cf36ca6
	golang.org/x/net v0.40.0
	gorm.io/gorm v1.30.0
)

require (
	github.com/agnivade/levenshtein v1.2.1 // indirect
	github.com/bytedance/sonic v1.13.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/gin-contrib/sse v1.0.0 // indirect
	github.com/go-ini/ini v1.67.0 // indirect
	github.com/go-viper/mapstructure/v2 v2.2.1 // indirect
	github.com/gofrs/uuid/v5 v5.2.0 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.7 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761 // indirect
	github.com/jackc/pgx/v5 v5.7.4 // indirect
	github.com/jackc/puddle/v2 v2.2.2 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/joho/godotenv v1.5.1 // indirect
	github.com/kelseyhightower/envconfig v1.4.0 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/klauspost/cpuid/v2 v2.2.10 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/minio/crc64nvme v1.0.1 // indirect
	github.com/minio/md5-simd v1.1.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/nats-io/nkeys v0.4.11 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/pelletier/go-toml/v2 v2.2.3 // indirect
	github.com/rogpeppe/go-internal v1.14.1 // indirect
	github.com/rs/xid v1.6.0 // indirect
	github.com/sosodev/duration v1.3.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	go.jetify.com/typeid v1.3.0 // indirect
	go.uber.org/dig v1.19.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/arch v0.16.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/postgres v1.5.11 // indirect
)
