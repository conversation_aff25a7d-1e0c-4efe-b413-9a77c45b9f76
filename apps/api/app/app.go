package app

import (
	"github.com/coorpe-app/coorpe/internal/delivery/graphql"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/directives"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/resolvers"
	"github.com/coorpe-app/coorpe/internal/delivery/httpserver"
	authroutes "github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes/auth"
	channelroutes "github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes/channels"
	channelmembersroutes "github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes/channels_members"
	mailroutes "github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes/mail"
	messageroutes "github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes/messages"
	workspaceroutes "github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes/workspaces"
	workspacemembersroutes "github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes/workspaces_members"
	subscriptionsrouter "github.com/coorpe-app/coorpe/internal/delivery/nats-router"
	"github.com/coorpe-app/coorpe/internal/delivery/ws"
	"github.com/coorpe-app/coorpe/internal/huma"
	"github.com/coorpe-app/coorpe/internal/mailer"
	"github.com/coorpe-app/coorpe/internal/minio"
	"github.com/coorpe-app/coorpe/internal/services/auth"
	"github.com/coorpe-app/coorpe/internal/services/boards"
	"github.com/coorpe-app/coorpe/internal/services/channels"
	"github.com/coorpe-app/coorpe/internal/services/channels_members"
	"github.com/coorpe-app/coorpe/internal/services/columns"
	"github.com/coorpe-app/coorpe/internal/services/issues"
	"github.com/coorpe-app/coorpe/internal/services/mail"
	"github.com/coorpe-app/coorpe/internal/services/messages"
	"github.com/coorpe-app/coorpe/internal/services/workspaces"
	"github.com/coorpe-app/coorpe/internal/services/workspaces_members"
	"github.com/coorpe-app/coorpe/internal/sessions"
	"github.com/coorpe-app/coorpe/libs/baseapp"
	kanbanboardsrepository "github.com/coorpe-app/coorpe/libs/repositories/boards"
	channelrepository "github.com/coorpe-app/coorpe/libs/repositories/channels"
	channelmembersrepository "github.com/coorpe-app/coorpe/libs/repositories/channels_members"
	kanbancolumnsrepository "github.com/coorpe-app/coorpe/libs/repositories/columns"
	workspaceissuesrepository "github.com/coorpe-app/coorpe/libs/repositories/issues"
	messagerepository "github.com/coorpe-app/coorpe/libs/repositories/message"
	userrepository "github.com/coorpe-app/coorpe/libs/repositories/user"
	workspacerepository "github.com/coorpe-app/coorpe/libs/repositories/workspaces"
	workspacemembersrepository "github.com/coorpe-app/coorpe/libs/repositories/workspaces_members"
	"go.uber.org/fx"
)

const serviceName = "api"

var App = fx.Module(
	serviceName,
	baseapp.CreateBaseApp(serviceName),
	fx.Provide(
		minio.New,
		mailer.New,
	),

	fx.Provide(
		fx.Annotate(
			workspacerepository.New,
			fx.As(new(workspacerepository.Repository)),
		),
		fx.Annotate(
			userrepository.New,
			fx.As(new(userrepository.Repository)),
		),
		fx.Annotate(
			channelrepository.New,
			fx.As(new(channelrepository.Repository)),
		),
		fx.Annotate(
			channelmembersrepository.New,
			fx.As(new(channelmembersrepository.Repository)),
		),
		fx.Annotate(
			messagerepository.New,
			fx.As(new(messagerepository.Repository)),
		),
		fx.Annotate(
			workspacemembersrepository.New,
			fx.As(new(workspacemembersrepository.Repository)),
		),
		fx.Annotate(
			kanbanboardsrepository.New,
			fx.As(new(kanbanboardsrepository.Repository)),
		),
		fx.Annotate(
			kanbancolumnsrepository.New,
			fx.As(new(kanbancolumnsrepository.Repository)),
		),
		fx.Annotate(
			workspaceissuesrepository.New,
			fx.As(new(workspaceissuesrepository.Repository)),
		),
	),

	// Services
	fx.Provide(
		auth.New,
		mail.New,
		workspaces.New,
		workspaces_members.New,
		channels.New,
		channels_members.New,
		messages.New,
		boards.New,
		columns.New,
		issues.New,
	),

	// Delivery
	fx.Provide(
		huma.New,
		httpserver.New,
		sessions.New,
		ws.New,
		resolvers.New,
		directives.New,
		fx.Annotate(
			subscriptionsrouter.NewNatsSubscription,
			fx.As(new(subscriptionsrouter.Router)),
		),
	),

	fx.Invoke(
		graphql.New,
		authroutes.New,
		channelroutes.New,
		workspaceroutes.New,
		workspacemembersroutes.New,
		channelmembersroutes.New,
		messageroutes.New,
		mailroutes.New,
	),
)
