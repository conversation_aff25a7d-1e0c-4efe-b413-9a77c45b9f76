package workspaces

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/internal/services/workspaces"
	"github.com/coorpe-app/coorpe/internal/sessions"
	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"net/http"
)

type Params struct {
	fx.In

	Api              huma.API
	WorkspaceService *workspaces.Service
	Session          *sessions.Sessions
}

func New(p Params) {
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "create-workspace",
			Summary:     "Create a workspace",
			Description: "Creates a new workspace",
			Method:      http.MethodPost,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces",
			Security: []map[string][]string{
				{"authentication": {}},
			},
			MaxBodyBytes: 1024 * 1024 * 5, // 10MB
		}, func(ctx context.Context, input *struct {
			RawBody huma.MultipartFormFiles[struct {
				Icon        huma.FormFile            `form:"icon" contentType:"text/plain" nulable:"true" required:"false"`
				Name        string                   `form:"name"`
				Description string                   `form:"description" nullable:"true" required:"false"`
				Type        entity.WorkspaceTypeEnum `form:"type" default:"SCRUM" example:"SCRUM"`
			}]
		}) (*createWorkspaceOutput, error) {
			formData := input.RawBody.Data()

			var url *string
			if formData.Icon.IsSet {
				file := formData.Icon

				iconUrl, err := p.WorkspaceService.CreateIcon(
					ctx, entity.File{
						Content:     file.File,
						Filename:    file.Filename,
						Size:        file.Size,
						ContentType: file.ContentType,
					},
				)
				if err != nil {
					return nil, huma.Error500InternalServerError("failed to upload icon", err)
				}

				url = &iconUrl
			}

			data, err := p.WorkspaceService.Create(
				ctx, workspaces.CreateInput{
					Name:        formData.Name,
					Type:        formData.Type,
					Description: &formData.Description,
					IconURL:     url,
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to create workspaces", err)
			}

			return &createWorkspaceOutput{
				Body: workspaceOutputDto{
					ID:          data.ID,
					Name:        data.Name,
					Type:        string(data.Type),
					Description: data.Description,
					IconURL:     data.IconURL,
				},
			}, nil
		},
	)
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "update-workspace",
			Summary:     "Update a workspace",
			Description: "Updates an existing workspace by ID",
			Method:      http.MethodPut,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces/{id}",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *struct {
			ID   string `path:"id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
			Body struct {
				Name        string  `json:"name" required:"true" example:"Updated Workspace" minLength:"1" maxLength:"100"`
				Description *string `json:"description" required:"false" nullable:"true" example:"This is my updated workspaces"`
			}
		}) (*updateWorkspaceOutput, error) {
			updateInput := workspaces.UpdateInput{
				Name:        input.Body.Name,
				Description: input.Body.Description,
			}

			data, err := p.WorkspaceService.Update(ctx, input.ID, updateInput)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to update workspaces", err)
			}

			return &updateWorkspaceOutput{
				Body: workspaceOutputDto{
					ID:          data.ID,
					Name:        data.Name,
					Description: data.Description,
					IconURL:     data.IconURL,
				},
			}, nil
		},
	)

	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "get-workspace-by-id",
			Summary:     "Get workspace by id",
			Description: "Retrieves a workspace by its id",
			Method:      http.MethodGet,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces/id/{id}",
		}, func(ctx context.Context, input *struct {
			ID string `path:"id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
		}) (*getWorkspaceOutput, error) {
			data, err := p.WorkspaceService.GetByID(ctx, input.ID)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to get workspaces", err)
			}

			return &getWorkspaceOutput{
				Body: workspaceOutputDto{
					ID:          data.ID,
					Name:        data.Name,
					Description: data.Description,
					IconURL:     data.IconURL,
					Type:        string(data.Type),
				},
			}, nil
		},
	)
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "get-all-workspaces",
			Summary:     "Get all workspaces",
			Description: "Retrieves all workspaces for the authenticated user",
			Method:      http.MethodGet,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *struct {
			Page  uint `json:"page" query:"page" example:"1" default:"1" minimum:"1"`
			Limit uint `json:"limit" query:"limit" example:"20" default:"20"`
		}) (*getWorkspacesOutput, error) {
			user, err := p.Session.GetAuthenticatedUser(ctx)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to get user", err)
			}

			data, err := p.WorkspaceService.GetMany(
				ctx,
				workspaces.GetManyInput{
					UserID: user.ID,
					Page:   int(input.Page),
					Limit:  int(input.Limit),
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to get workspaces", err)
			}

			result := make([]workspaceOutputDto, 0, len(data.Items))
			for _, item := range data.Items {
				result = append(
					result,
					workspaceOutputDto{
						ID:          item.ID,
						Name:        item.Name,
						Description: item.Description,
						IconURL:     item.IconURL,
						Type:        string(item.Type),
					},
				)
			}

			return &getWorkspacesOutput{
				Body: getWorkspacesOutputDto{
					Items: result,
					Total: int(data.Total),
				},
			}, nil
		},
	)
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "send-workspaces-invite",
			Summary:     "Send workspaces invite",
			Description: "Sends an invitation to join a workspaces",
			Method:      http.MethodPost,
			Tags:        []string{"Workspace"},
			Path:        "/api/workspaces/invite",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *struct {
			Body struct {
				Emails      []string `json:"emails" example:"<EMAIL>" format:"email" uniqueItems:"true" maxItems:"10" minItems:"1"`
				WorkspaceID string   `json:"workspace_id" required:"true" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
			}
		}) (*sendInviteOutput, error) {
			user := ctx.Value("user").(entity.User)
			if user.ID == uuid.Nil {
				return nil, huma.Error401Unauthorized("user not authenticated")
			}

			inviteInput := workspaces.SendInviteInput{
				WorkspaceID: input.Body.WorkspaceID,
				Emails:      input.Body.Emails,
				UserID:      user.ID,
				UserName:    user.Name,
			}

			data, err := p.WorkspaceService.SendInvite(ctx, inviteInput)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to send invite", err)
			}

			return &sendInviteOutput{
				Body: sendInviteOutputDto{
					Sent:     data.Sent,
					Failed:   data.Failed,
					Existing: data.Existing,
				},
			}, nil
		},
	)
}

type createWorkspaceOutput struct {
	Body workspaceOutputDto
}

type updateWorkspaceOutput struct {
	Body workspaceOutputDto
}

type getWorkspaceOutput struct {
	Body workspaceOutputDto
}

type getWorkspacesOutput struct {
	Body getWorkspacesOutputDto
}

type workspaceOutputDto struct {
	ID          string  `json:"id" example:"kX9pQ"`
	Name        string  `json:"name" required:"true" example:"My Workspace"`
	Type        string  `json:"type" required:"true" example:"SCRUM" enum:"SCRUM,KANBAN"`
	Description *string `json:"description" required:"false" example:"This is my workspaces"`
	IconURL     *string `json:"iconUrl" required:"false" example:"https://cool.com/cool.png"`
}

type getWorkspacesOutputDto struct {
	Items []workspaceOutputDto `json:"items"`
	Total int                  `json:"total" example:"5"`
}

type sendInviteOutput struct {
	Body sendInviteOutputDto
}

type sendInviteOutputDto struct {
	Sent     []string `json:"sent"`
	Failed   []string `json:"failed"`
	Existing []string `json:"existing"`
}
