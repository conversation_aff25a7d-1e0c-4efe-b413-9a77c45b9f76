package messages

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/logger"
	"net/http"
	"time"

	"github.com/coorpe-app/coorpe/internal/delivery/ws"
	"go.uber.org/fx"

	myhttp "github.com/coorpe-app/coorpe/internal/delivery/httpserver"
	"github.com/coorpe-app/coorpe/internal/services/messages"
	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
)

type Params struct {
	fx.In

	Api     huma.API
	Service *messages.Service
	Router  *myhttp.Server
	Ws      *ws.Manager
	Logger  logger.Logger
}

type Messages struct {
	api      huma.API
	service  *messages.Service
	router   *myhttp.Server
	upgrader *ws.Manager
	logger   logger.Logger
}

func New(p Params) *Messages {
	c := &Messages{
		service:  p.Service,
		api:      p.Api,
		router:   p.Router,
		upgrader: p.Ws,
		logger:   p.<PERSON><PERSON>,
	}

	p.Router.GET("/wss/channel/:channelId/messages", c.handleMessagesWebSocket)

	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "send-channel-messages",
			Summary:     "Send a channel message",
			Description: "Sends a channel message",
			Method:      http.MethodPost,
			Tags:        []string{"Message"},
			Path:        "/api/messages",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *sendMessageInput) (*sendMessageOutput, error) {
			msg, err := p.Service.Create(
				ctx, messages.SendMessageInput{
					Body:           input.Body.Body,
					WorkspaceID:    input.Body.WorkspaceID,
					ChannelID:      input.Body.ChannelID,
					ConversationID: input.Body.ConversationID,
					Type:           input.Body.Type,
				},
			)

			if err != nil {
				return nil, huma.Error500InternalServerError("failed to create messages", err)
			}

			return &sendMessageOutput{
				Body: sendMessageOutputDto{
					Body:           msg.Body,
					WorkspaceID:    msg.WorkspaceID,
					ChannelID:      msg.ChannelID,
					ConversationID: msg.ConversationID,
					Type:           msg.Type,
				},
			}, nil
		},
	)
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "get-channel -messages",
			Summary:     "Get channel messages",
			Description: "Retrieves messages from a specific channel with pagination",
			Method:      http.MethodGet,
			Tags:        []string{"Message"},
			Path:        "/api/messages/channel/{channelId}",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, input *getMessagesInput) (*getMessagesOutput, error) {
			data, err := p.Service.GetMany(
				messages.GetManyInput{
					ChannelID: input.ChannelID,
					Page:      int(input.Page),
					Limit:     int(input.Limit),
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("failed to get channels messages", err)
			}

			result := make([]messageOutputDto, 0, len(data.Items))
			for _, item := range data.Items {
				result = append(
					result,
					messageOutputDto{
						ID:             item.ID,
						Body:           item.Body,
						WorkspaceID:    item.WorkspaceID,
						ChannelID:      item.ChannelID,
						ConversationID: item.ConversationID,
						SenderID:       item.Sender.ID,
						SenderName:     item.Sender.Name,
						SenderAvatar:   item.Sender.AvatarURL,
						CreatedAt:      item.CreatedAt,
						UpdatedAt:      item.UpdatedAt,
						Type:           item.Type,
					},
				)
			}

			return &getMessagesOutput{
				Body: getMessagesOutputDto{
					Items: result,
					Total: int(data.Total),
				},
			}, nil
		},
	)

	return c
}

type messageOutputDto struct {
	ID             uuid.UUID `json:"id" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	Body           string    `json:"body" example:"Hello, world!"`
	WorkspaceID    string    `json:"workspaceId" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	ChannelID      *string   `json:"channelId" example:"d2a5f23fsa"`
	ConversationID *string   `json:"conversationId" example:"d2a5f23fsa"`
	Type           string    `json:"type" example:"text"`
	SenderID       uuid.UUID `json:"senderId" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	SenderName     string    `json:"senderName" example:"Ricardo Vinicius"`
	SenderAvatar   string    `json:"senderAvatar" example:"https://cool.com/cool.png"`
	CreatedAt      time.Time `json:"createdAt" example:"2021-01-01T00:00:00Z"`
	UpdatedAt      time.Time `json:"updatedAt" example:"2021-01-01T00:00:00Z"`
}

type sendMessageInputDto struct {
	Body           string  `json:"body" required:"true" example:"Hello, world!" minLength:"1"`
	WorkspaceID    string  `json:"workspaceId" required:"true" example:"d2a5f23fsa"`
	ChannelID      *string `json:"channelId" required:"false" nullable:"true" example:"d2a5f23fsa"`
	ConversationID *string `json:"conversationId" required:"false" nullable:"true" example:"d2a5f23fsa"`
	Type           string  `json:"type" required:"true" example:"text" enum:"text,image,file"`
}

type sendMessageOutput struct {
	Body sendMessageOutputDto
}

type sendMessageOutputDto struct {
	Body           string  `json:"body" example:"Hello, world!"`
	WorkspaceID    string  `json:"workspace_id" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	ChannelID      *string `json:"channel_id" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	ConversationID *string `json:"conversation_id" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	Type           string  `json:"type" required:"true" example:"text" enum:"text,image,file"`
}

type getMessagesInput struct {
	ChannelID string `path:"channelId" required:"true" example:"d2a5f23fsa"`
	Page      uint   `query:"page" default:"1" minimum:"1" example:"1"`
	Limit     uint   `query:"limit" default:"20" example:"20"`
}

type getMessagesOutput struct {
	Body getMessagesOutputDto
}

type getMessagesOutputDto struct {
	Total int                `json:"total" example:"10"`
	Items []messageOutputDto `json:"items"`
}

type sendMessageInput struct {
	Body sendMessageInputDto
}
