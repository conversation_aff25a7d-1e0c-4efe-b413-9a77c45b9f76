package auth

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/delivery/httpserver/routes"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/internal/services/auth"
	"github.com/coorpe-app/coorpe/internal/sessions"
	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"net/http"
	"time"
)

type Params struct {
	fx.In

	Api      huma.API
	Service  *auth.Service
	Sessions *sessions.Sessions
}

func New(p Params) {
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "register",
			Summary:     "Register user",
			Method:      http.MethodPost,
			Tags:        []string{"Authentication"},
			Path:        "/api/auth/register",
		}, func(ctx context.Context, input *struct {
			Body registerInputDto
		}) (*registerOutput, error) {
			result, err := p.Service.Register(
				ctx, auth.RegisterInput{
					Name:     input.Body.Name,
					Email:    input.Body.Email,
					Password: input.Body.Password,
					Birthday: input.Body.Birthday,
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("Cannot register user", err)
			}
			return &registerOutput{registerOutputDto{
				Data: result,
			}}, nil
		},
	)
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "login",
			Summary:     "Login user",
			Method:      http.MethodPost,
			Tags:        []string{"Authentication"},
			Path:        "/api/auth/login",
		}, func(ctx context.Context, input *struct {
			Body loginInputDto
		}) (*loginOutput, error) {
			userID, err := p.Service.Login(
				auth.LoginInput{
					Email:    input.Body.Email,
					Password: input.Body.Password,
				},
			)
			if err != nil {
				return nil, huma.Error500InternalServerError("Cannot login", err)
			}

			p.Sessions.Put(ctx, "userID", userID.String())

			resp := &loginOutput{}
			resp.Body.UserID = userID.String()

			return resp, nil
		},
	)
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "me",
			Summary:     "Get current user",
			Method:      http.MethodGet,
			Tags:        []string{"Authentication"},
			Path:        "/api/auth/me",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, i *routes.EmptyBody) (*meOutput, error) {
			user, err := p.Sessions.GetAuthenticatedUser(ctx)
			if err != nil {
				return nil, huma.Error500InternalServerError("cannot get user", err)
			}

			return &meOutput{Body: meOutputDto{
				ID:            user.ID,
				Name:          user.Name,
				Email:         user.Email,
				Birthday:      user.Birthday,
				AvatarURL:     user.AvatarURL,
				CreatedAt:     user.CreatedAt,
				UpdatedAt:     user.UpdatedAt,
				EmailVerified: user.EmailVerified,
			}}, nil
		},
	)
	huma.Register(
		p.Api,
		huma.Operation{
			OperationID: "logout",
			Summary:     "Logout user",
			Method:      http.MethodPost,
			Tags:        []string{"Authentication"},
			Path:        "/api/auth/logout",
			Security: []map[string][]string{
				{"authentication": {}},
			},
		}, func(ctx context.Context, i *routes.EmptyBody) (*logoutOutput, error) {
			err := p.Sessions.Destroy(ctx)
			if err != nil {
				return nil, huma.Error500InternalServerError("Error parsing session cookie", err)
			}

			return &logoutOutput{Body: logoutOutputDto{
				Success: true,
			}}, nil
		},
	)
}

type logoutOutput struct {
	Body logoutOutputDto
}

type logoutOutputDto struct {
	Success bool `json:"success"`
}

type loginInputDto struct {
	Email    string `json:"email" required:"true" minLength:"1" maxLength:"255" example:"<EMAIL>"`
	Password string `json:"password" required:"true" minLength:"1" maxLength:"100" example:"123123123"`
}

type registerInputDto struct {
	Name     string    `json:"name" required:"true" minLength:"1" maxLength:"100" example:"johndoe"`
	Email    string    `json:"email" required:"true" minLength:"1" maxLength:"255" example:"<EMAIL>" format:"email"`
	Password string    `json:"password" required:"true" minLength:"8" maxLength:"128" example:"123123123"`
	Birthday time.Time `json:"birthday" required:"true" example:"2000-01-01" format:"date-time"`
}

type loginOutput struct {
	Body loginOutputDto
}

type loginOutputDto struct {
	UserID string `json:"userID"`
}

type registerOutput struct {
	Body registerOutputDto
}

type registerOutputDto struct {
	Data entity.User `json:"data"`
}

type meOutput struct {
	Body meOutputDto
}

type meOutputDto struct {
	ID            uuid.UUID `json:"id" example:"a1b2c3d4-e5f6-7890-abcd-ef1234567890"`
	Name          string    `json:"name" example:"johndoe"`
	Email         string    `json:"email" example:"<EMAIL>"`
	Birthday      time.Time `json:"birthday" example:"2000-01-01" format:"date-time"`
	AvatarURL     string    `json:"avatarURL" example:"https://cool.com/cool.png"`
	CreatedAt     time.Time `json:"createdAt" example:"2021-01-01T00:00:00Z"`
	UpdatedAt     time.Time `json:"updatedAt" example:"2021-01-01T00:00:00Z"`
	EmailVerified bool      `json:"emailVerified" example:"true"`
}
