package channels_members

import (
	"context"
	"net/http"
	"time"

	"github.com/coorpe-app/coorpe/internal/services/channels_members"
	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
)

func New(api huma.API, service *channels_members.Service) {
	huma.Register(
		api,
		huma.Operation{
			OperationID: "get-channel-members",
			Summary:     "Get channel members",
			Method:      http.MethodGet,
			Tags:        []string{"ChannelMembers"},
			Path:        "/api/channel/{channelId}/members",
		}, func(ctx context.Context, input *struct {
			ChannelID string `path:"channelId"`
		}) (*getChannelMembersOutput, error) {
			members, err := service.GetMany(ctx, input.ChannelID)
			if err != nil {
				return nil, err
			}

			result := make([]channelMembersOutputDto, 0, len(members))
			for _, member := range members {
				result = append(
					result, channelMembersOutputDto{
						ID:         member.ID,
						Name:       member.Name,
						Birthday:   member.Birthday,
						Email:      member.Email,
						AvatarURL:  member.AvatarURL,
						IsMuted:    member.IsMuted,
						LastActive: member.LastActive,
					},
				)
			}

			return &getChannelMembersOutput{
				Body: getChannelMembersOutputDto{
					Total: len(result),
					Items: result,
				},
			}, nil
		},
	)
}

type channelMembersOutputDto struct {
	ID         uuid.UUID  `json:"id" example:"1"`
	Name       string     `json:"name" example:"Cool"`
	Birthday   time.Time  `json:"birthday" example:"1990-01-01"`
	Email      string     `json:"email" example:"<EMAIL>"`
	AvatarURL  *string    `json:"avatarUrl" example:"https://cool.com/cool.png" required:"false" nullable:"true"`
	IsMuted    bool       `json:"isMuted" example:"false"`
	LastActive *time.Time `json:"lastActive" example:"2021-01-01T00:00:00Z"`
}

type getChannelMembersOutput struct {
	Body getChannelMembersOutputDto
}

type getChannelMembersOutputDto struct {
	Total int                       `json:"total" example:"1"`
	Items []channelMembersOutputDto `json:"items"`
}
