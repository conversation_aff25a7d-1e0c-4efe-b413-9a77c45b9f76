package mappers

import (
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
	"github.com/coorpe-app/coorpe/internal/entity"
)

var mapTaskTypeToGql = map[entity.WorkspaceIssueTypeEnum]gqlmodel.TaskTypeEnum{
	entity.WorkspaceIssueTypeBug:       gqlmodel.TaskTypeEnumBug,
	entity.WorkspaceIssueTypeTask:      gqlmodel.TaskTypeEnumTask,
	entity.WorkspaceIssueTypeUserStory: gqlmodel.TaskTypeEnumUserStory,
}

var mapTaskStatusToGql = map[entity.WorkspaceIssueStatusEnum]gqlmodel.TaskStatusEnum{
	entity.WorkspaceIssueStatusTypeOpen:       gqlmodel.TaskStatusEnumOpen,
	entity.WorkspaceIssueStatusTypeInProgress: gqlmodel.TaskStatusEnumInProgress,
	entity.WorkspaceIssueStatusTypeCompleted:  gqlmodel.TaskStatusEnumCompleted,
	entity.WorkspaceIssueStatusTypeCancelled:  gqlmodel.TaskStatusEnumClosed,
}

func TaskToGraphQL(task entity.WorkspaceIssue) gqlmodel.Task {
	return gqlmodel.Task{
		ID:          task.ID,
		Title:       task.Title,
		Description: task.Description,
		Status:      gqlmodel.TaskStatusEnum(task.Status),
		Type:        gqlmodel.TaskTypeEnum(task.Type),
		SprintID:    task.SprintID,
		ParentID:    task.ParentID,
		WorkspaceID: task.WorkspaceID,
		AssigneeID:  task.AssigneeID,
		CreatedAt:   task.CreatedAt,
		UpdatedAt:   task.UpdatedAt,
	}
}

func TasksToGraphQL(tasks []entity.WorkspaceIssue) []gqlmodel.Task {
	var gqlTasks []gqlmodel.Task
	for _, task := range tasks {
		gqlTasks = append(
			gqlTasks, gqlmodel.Task{
				ID:          task.ID,
				Title:       task.Title,
				Description: task.Description,
				Status:      gqlmodel.TaskStatusEnum(task.Status),
				Type:        gqlmodel.TaskTypeEnum(task.Type),
				SprintID:    task.SprintID,
				ParentID:    task.ParentID,
				WorkspaceID: task.WorkspaceID,
				AssigneeID:  task.AssigneeID,
				CreatedAt:   task.CreatedAt,
				UpdatedAt:   task.UpdatedAt,
			},
		)
	}
	return gqlTasks
}

func TaskTypeToGql(t entity.WorkspaceIssueTypeEnum) gqlmodel.TaskTypeEnum {
	return mapTaskTypeToGql[t]
}

func TaskTypeToEntity(t gqlmodel.TaskTypeEnum) entity.WorkspaceIssueTypeEnum {
	for k, v := range mapTaskTypeToGql {
		if v == t {
			return k
		}
	}
	return entity.WorkspaceIssueTypeNil
}

func TaskStatusToGql(t entity.WorkspaceIssueStatusEnum) gqlmodel.TaskStatusEnum {
	return mapTaskStatusToGql[t]
}

func TaskStatusToEntity(t gqlmodel.TaskStatusEnum) entity.WorkspaceIssueStatusEnum {
	for k, v := range mapTaskStatusToGql {
		if v == t {
			return k
		}
	}
	return entity.WorkspaceIssueStatusNil
}
