package mappers

import (
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
	"github.com/coorpe-app/coorpe/internal/entity"
)

func BoardToGraphQL(board entity.WorkspaceBoard) gqlmodel.Board {
	return gqlmodel.Board{
		ID:          board.ID,
		Title:       board.Title,
		Description: board.Description,
		OwnerID:     board.OwnerID,
		CreatedAt:   board.CreatedAt,
		UpdatedAt:   board.UpdatedAt,
		Columns:     ColumnsToGraphQL(board.Columns),
	}
}

func ColumnToGraphQL(column entity.WorkspaceKanbanColumn) gqlmodel.Column {
	return gqlmodel.Column{
		ID:       column.ID,
		Title:    column.Title,
		Position: column.Position,
		BoardID:  column.BoardID,
		Tasks:    IssuesToGraphQL(column.Tasks),
	}
}

func ColumnsToGraphQL(columns []entity.WorkspaceKanbanColumn) []gqlmodel.Column {
	var gqlColumns []gqlmodel.Column
	for _, column := range columns {
		gqlColumns = append(
			gqlColumns, gqlmodel.Column{
				ID:       column.ID,
				Title:    column.Title,
				Position: column.Position,
				BoardID:  column.BoardID,
				Tasks:    IssuesToGraphQL(column.Tasks),
			},
		)
	}
	return gqlColumns
}
