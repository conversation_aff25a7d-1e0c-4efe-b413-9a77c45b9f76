extend type Query {
    storiesByEpic(epicId: ID!): [Story!]! @isAuthenticated
    storiesBySprint(sprintId: ID!): [Story!]! @isAuthenticated
    story(id: ID!): Story @isAuthenticated
}

extend type Mutation {
    createStory(input: CreateStoryInput!): Story!
    updateStory(input: UpdateStoryInput!): Story!
    deleteStory(id: ID!): Boolean!
}

extend type Subscription {
    storyUpdated(sprintId: ID!): Story! @isAuthenticated
}

type Story {
    id: UUID!
    title: String!
    description: String
    status: StoryStatus!
    storyPoints: Int
    priority: Int!
    epicId: ID
    sprintId: ID
    assigneeId: UUID
    issues: [Issue!]!
    createdAt: Time!
    updatedAt: Time!
}

enum StoryStatus {
    TO_DO
    IN_PROGRESS
    IN_REVIEW
    DONE
}

input CreateStoryInput {
    title: String! @validate(constraint: "min=1,max=255")
    description: String @validate(constraint: "max=500")
    status: StoryStatus = TO_DO
    storyPoints: Int
    priority: Int!
    epicId: ID
    sprintId: ID
    assigneeId: UUID!
}

input UpdateStoryInput {
    id: ID!
    title: String! @validate(constraint: "min=1,max=255")
    description: String @validate(constraint: "max=500")
    status: StoryStatus
    storyPoints: Int
    priority: Int
    epicId: ID
    sprintId: ID
    assigneeId: UUID
}