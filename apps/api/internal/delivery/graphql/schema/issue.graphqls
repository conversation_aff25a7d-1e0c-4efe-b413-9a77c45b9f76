extend type Query {
    issue(id: ID!): Issue @isAuthenticated
    issuesByColumn(columnId: ID!): [Issue!]! @isAuthenticated
    issuesByStory(parentId: ID!): [Issue!]! @isAuthenticated
    issuesBySprint(sprintId: ID!): [Issue!]! @isAuthenticated
    issuesBacklog(workspaceId: ID!): [Issue!]! @isAuthenticated
}

extend type Mutation {
    createIssue(input: CreateIssueInput!): Issue! @isAuthenticated
    updateIssue(id: ID!, input: UpdateIssueInput!): Issue! @isAuthenticated
    deleteIssue(id: ID!): Boolean! @isAuthenticated
}

extend type Subscription {
    issueUpdated(boardId: ID!): Issue! @isAuthenticated
}

type Issue {
    id: ID!
    title: String!
    description: String
    status: IssueStatusEnum!
    type: IssueTypeEnum!
    createdAt: Time!
    updatedAt: Time!
    assigneeId: UUID
    sprintId: ID
    parentId: ID
    workspaceId: ID!
}

enum IssueStatusEnum {
    OPEN
    IN_PROGRESS
    COMPLETED
    CLOSED
}

enum IssueTypeEnum {
    USER_STORY
    BUG
    TASK
}

input CreateIssueInput {
    title: String! @validate(constraint: "min=1,max=255")
    description: String @validate(constraint: "max=500")
    assigneeId: UUID
    parentId: ID
    sprintId: ID
    workspaceId: ID!
    status: IssueStatusEnum! = OPEN
    type: IssueTypeEnum! = TASK
}

input UpdateIssueInput {
    title: String @validate(constraint: "min=1,max=255")
    description: String @validate(constraint: "max=500")
    status: IssueStatusEnum
    type: IssueTypeEnum
    parentId: ID
    assigneeId: UUID
    sprintId: ID
}