package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.74

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/mappers"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/internal/services/tasks"
	kanban_tasks_bus "github.com/coorpe-app/coorpe/libs/bus/tasks"
	"github.com/goccy/go-json"
)

// CreateTask is the resolver for the createTask field.
func (r *mutationResolver) CreateTask(ctx context.Context, input gqlmodel.CreateTaskInput) (*gqlmodel.Task, error) {
	createdTask := tasks.CreateInput{
		Title:       input.Title,
		Description: input.Description.Value(),
		ParentID:    input.ParentID.Value(),
		SprintID:    input.SprintID.Value(),
		WorkspaceID: input.WorkspaceID,
		AssigneeID:  input.AssigneeID.Value(),
		Status:      mappers.TaskStatusToEntity(input.Status),
		Type:        mappers.TaskTypeToEntity(input.Type),
	}

	task, err := r.deps.TasksService.Create(ctx, createdTask)
	if err != nil {
		return nil, err
	}

	converted := mappers.TaskToGraphQL(task)
	return &converted, nil
}

// UpdateTask is the resolver for the updateTask field.
func (r *mutationResolver) UpdateTask(ctx context.Context, id string, input gqlmodel.UpdateTaskInput) (*gqlmodel.Task, error) {
	updatedTask := tasks.UpdateInput{}

	if input.Title.IsSet() {
		updatedTask.Title = input.Title.Value()
	}

	if input.Description.IsSet() {
		updatedTask.Description = input.Description.Value()
	}

	if input.Status.IsSet() {
		status := entity.WorkspaceTaskStatusEnum(*input.Status.Value())
		updatedTask.Status = &status
	}

	if input.Type.IsSet() {
		taskType := entity.WorkspaceTaskTypeEnum(*input.Type.Value())
		updatedTask.Type = &taskType
	}

	if input.AssigneeID.IsSet() {
		updatedTask.AssigneeID = input.AssigneeID.Value()
	}

	if input.SprintID.IsSet() {
		updatedTask.SprintID = input.SprintID.Value()
	}

	if input.ParentID.IsSet() {
		updatedTask.ParentID = input.ParentID.Value()
	}

	task, err := r.deps.TasksService.Update(
		ctx,
		id,
		updatedTask,
	)
	if err != nil {
		return nil, err
	}

	converted := mappers.TaskToGraphQL(task)
	return &converted, nil
}

// DeleteTask is the resolver for the deleteTask field.
func (r *mutationResolver) DeleteTask(ctx context.Context, id string) (bool, error) {
	err := r.deps.TasksService.Delete(ctx, id)
	if err != nil {
		return false, err
	}
	return true, nil
}

// Task is the resolver for the task field.
func (r *queryResolver) Task(ctx context.Context, id string) (*gqlmodel.Task, error) {
	task, err := r.deps.TasksService.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	converted := mappers.TaskToGraphQL(task)
	return &converted, nil
}

// TasksByColumn is the resolver for the tasksByColumn field.
func (r *queryResolver) TasksByColumn(ctx context.Context, columnID string) ([]gqlmodel.Task, error) {
	dbTasks, err := r.deps.TasksService.GetByColumnID(ctx, columnID)
	if err != nil {
		return nil, err
	}

	converted := mappers.TasksToGraphQL(dbTasks)
	return converted, nil
}

// TasksByStory is the resolver for the tasksByStory field.
func (r *queryResolver) TasksByStory(ctx context.Context, parentID string) ([]gqlmodel.Task, error) {
	panic(fmt.Errorf("not implemented: TasksByStory - tasksByStory"))
}

// TasksBySprint is the resolver for the tasksBySprint field.
func (r *queryResolver) TasksBySprint(ctx context.Context, sprintID string) ([]gqlmodel.Task, error) {
	panic(fmt.Errorf("not implemented: TasksBySprint - tasksBySprint"))
}

// TasksBacklog is the resolver for the tasksBacklog field.
func (r *queryResolver) TasksBacklog(ctx context.Context, workspaceID string) ([]gqlmodel.Task, error) {
	backlogTasks, err := r.deps.TasksService.GetBacklogByWorkspaceID(ctx, workspaceID)
	if err != nil {
		return nil, err
	}

	converted := mappers.TasksToGraphQL(backlogTasks)
	return converted, nil
}

// TaskUpdated is the resolver for the taskUpdated field.
func (r *subscriptionResolver) TaskUpdated(ctx context.Context, boardID string) (<-chan *gqlmodel.Task, error) {
	channel := make(chan *gqlmodel.Task, 1)

	go func() {
		sub, err := r.deps.Nats.Subscribe(
			[]string{
				tasks.CreateUpdatedTaskSubscriptionKeyByBoardID(boardID),
			},
		)
		if err != nil {
			r.deps.Logger.Error("cannot subscribe to kanban task updated", slog.Any("err", err))
			return
		}
		defer func() {
			sub.Unsubscribe()
			close(channel)
		}()

		for {
			select {
			case <-ctx.Done():
				return
			case data := <-sub.GetChannel():
				var task kanban_tasks_bus.Task
				if err := json.Unmarshal(data, &task); err != nil {
					r.deps.Logger.Error("cannot unmarshal kanban task", slog.Any("err", err))
					return
				}

				channel <- &gqlmodel.Task{
					Title:       task.Title,
					Description: task.Description,
					Status:      gqlmodel.TaskStatusEnum(task.Status),
					Type:        gqlmodel.TaskTypeEnum(task.Type),
					WorkspaceID: task.WorkspaceID,
					SprintID:    task.SprintID,
					ParentID:    task.ParentID,
					AssigneeID:  task.AssigneeID,
					CreatedAt:   task.CreatedAt,
					UpdatedAt:   task.UpdatedAt,
				}
			}
		}
	}()

	return channel, nil
}
