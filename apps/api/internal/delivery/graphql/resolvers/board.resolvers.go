package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.74

import (
	"context"
	"log/slog"

	"github.com/coorpe-app/coorpe/internal/delivery/graphql/gqlmodel"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/graph"
	"github.com/coorpe-app/coorpe/internal/delivery/graphql/mappers"
	"github.com/coorpe-app/coorpe/internal/services/boards"
	kanban_boards_bus "github.com/coorpe-app/coorpe/libs/bus/boards"
	"github.com/goccy/go-json"
)

// Columns is the resolver for the columns field.
func (r *boardResolver) Columns(ctx context.Context, obj *gqlmodel.Board) ([]gqlmodel.Column, error) {
	dbColumns, err := r.deps.ColumnsService.GetMany(ctx, obj.ID)
	if err != nil {
		return nil, err
	}

	mappedColumns := make([]gqlmodel.Column, 0, len(dbColumns))
	for _, column := range dbColumns {
		mappedColumns = append(mappedColumns, mappers.ColumnToGraphQL(column))
	}

	return mappedColumns, nil
}

// CreateBoard is the resolver for the createBoard field.
func (r *mutationResolver) CreateBoard(ctx context.Context, input gqlmodel.CreateBoardInput) (*gqlmodel.Board, error) {
	user, err := r.deps.Sessions.GetAuthenticatedUser(ctx)
	if err != nil {
		return nil, err
	}

	board, err := r.deps.BoardsService.Create(
		ctx,
		boards.CreateInput{
			Title:       input.Title,
			Description: input.Description.Value(),
			WorkspaceID: input.WorkspaceID,
			OwnerID:     user.ID,
		},
	)
	if err != nil {
		return nil, err
	}

	converted := mappers.BoardToGraphQL(board)
	return &converted, nil
}

// UpdateBoard is the resolver for the updateBoard field.
func (r *mutationResolver) UpdateBoard(ctx context.Context, input gqlmodel.UpdateBoardInput) (*gqlmodel.Board, error) {
	updatedBoard := boards.UpdateInput{
		Title: input.Title,
	}

	if input.Description.IsSet() {
		updatedBoard.Description = input.Description.Value()
	}

	board, err := r.deps.BoardsService.Update(
		ctx,
		input.ID,
		updatedBoard,
	)
	if err != nil {
		return nil, err
	}

	converted := mappers.BoardToGraphQL(board)
	return &converted, nil
}

// DeleteBoard is the resolver for the deleteBoard field.
func (r *mutationResolver) DeleteBoard(ctx context.Context, id string) (bool, error) {
	err := r.deps.BoardsService.Delete(ctx, id)
	if err != nil {
		return false, err
	}
	return true, nil
}

// Board is the resolver for the board field.
func (r *queryResolver) Board(ctx context.Context, id string) (*gqlmodel.Board, error) {
	board, err := r.deps.BoardsService.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	converted := mappers.BoardToGraphQL(board)
	return &converted, nil
}

// Boards is the resolver for the boards field.
func (r *queryResolver) Boards(ctx context.Context, workspaceID string) ([]gqlmodel.Board, error) {
	dbBoards, err := r.deps.BoardsService.GetMany(ctx, workspaceID)
	if err != nil {
		return nil, err
	}

	mappedBoards := make([]gqlmodel.Board, 0, len(dbBoards))
	for _, board := range dbBoards {
		mappedBoards = append(mappedBoards, mappers.BoardToGraphQL(board))
	}

	return mappedBoards, nil
}

// BoardUpdated is the resolver for the boardUpdated field.
func (r *subscriptionResolver) BoardUpdated(ctx context.Context, id string) (<-chan *gqlmodel.Board, error) {
	channel := make(chan *gqlmodel.Board, 1)

	go func() {
		sub, err := r.deps.Nats.Subscribe(
			[]string{
				boards.CreateUpdatedBoardSubscriptionKeyByBoardID(id),
			},
		)
		if err != nil {
			r.deps.Logger.Error("cannot subscribe to kanban board updated", slog.Any("err", err))
			return
		}
		defer func() {
			sub.Unsubscribe()
			close(channel)
		}()

		for {
			select {
			case <-ctx.Done():
				return
			case data := <-sub.GetChannel():
				var board kanban_boards_bus.Board
				if err := json.Unmarshal(data, &board); err != nil {
					r.deps.Logger.Error("cannot unmarshal kanban board", slog.Any("err", err))
					return
				}

				channel <- &gqlmodel.Board{
					Title:       board.Title,
					Description: board.Description,
					OwnerID:     board.OwnerID,
					UpdatedAt:   board.UpdatedAt,
				}
			}
		}
	}()

	return channel, nil
}

// Board returns graph.BoardResolver implementation.
func (r *Resolver) Board() graph.BoardResolver { return &boardResolver{r} }

type boardResolver struct{ *Resolver }
