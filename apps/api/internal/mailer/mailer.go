package mailer

import (
	"fmt"
	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/coorpe-app/coorpe/libs/logger"
	"go.uber.org/fx"
	"go.uber.org/zap"
	"net/smtp"
	"os"
	"path/filepath"
	"strings"
)

type Mailer struct {
	config config.Config
	logger logger.Logger
}

type Params struct {
	fx.In

	Config config.Config
	Logger logger.Logger
}

func New(p Params) *Mailer {
	return &Mailer{config: p.Config, logger: p.<PERSON>gger}
}

func readTemplate(templatePath string, replacements map[string]string) (string, error) {
	htmlTemplate, err := os.ReadFile(templatePath)
	if err != nil {
		return "", err
	}

	htmlContent := string(htmlTemplate)
	for key, value := range replacements {
		htmlContent = strings.Replace(htmlContent, key, value, -1)
	}

	return htmlContent, nil
}

func (m *Mailer) SendVerificationEmail(to, token string) error {
	subject := "[Coorpe] Confirmação de E-mail"
	url := fmt.Sprintf("http://localhost:8080/api/auth/verify-email?token=%s", token)

	templatePath := filepath.Join("internal", "mailer", "templates", "email-verification.html")
	replacements := map[string]string{
		"{{.VerificationUrl}}": url,
	}

	htmlContent, err := readTemplate(templatePath, replacements)
	if err != nil {
		return fmt.Errorf("error reading template: %w", err)
	}

	err = m.sendMail(to, subject, htmlContent)

	return err
}

func (m *Mailer) SendEmailCode(to, code string) error {
	subject := "[Coorpe] Confirmação de E-mail"

	templatePath := filepath.Join("internal", "mailer", "templates", "email-code.html")
	replacements := map[string]string{
		"{{.VerificationCode}}": code,
	}

	htmlContent, err := readTemplate(templatePath, replacements)
	if err != nil {
		return fmt.Errorf("error reading template: %w", err)
	}

	return m.sendMail(to, subject, htmlContent)
}

type InviteToWorkspace struct {
	WorkspaceName string
	InviterName   string
	Token         string
}

func (m *Mailer) SendInviteToWorkspace(to string, invite InviteToWorkspace) error {
	subject := fmt.Sprintf("[Coorpe] Convite para o workspaces %s", invite.WorkspaceName)
	url := fmt.Sprintf("http://localhost:8080/api/workspace/accept-invite?token=%s", invite.Token)

	templatePath := filepath.Join("internal", "mailer", "templates", "invite-to-workspaces.html")
	htmlTemplate, err := os.ReadFile(templatePath)
	if err != nil {
		return fmt.Errorf("error reading template: %w", err)
	}

	title := fmt.Sprintf(
		"%s convidou você para entrar no workspaces %s 😀",
		invite.InviterName,
		invite.WorkspaceName,
	)

	htmlContent := string(htmlTemplate)
	htmlContent = strings.Replace(htmlContent, `href="#"`, fmt.Sprintf(`href="%s"`, url), 2)
	htmlContent = strings.Replace(htmlContent, `{{.Title}}`, title, 1)

	err = m.sendMail(to, subject, htmlContent)
	return err
}

func (m *Mailer) sendMail(to, subject, body string) error {
	from := m.config.GmailMail
	password := m.config.GmailPassword
	host := m.config.GmailServerHost
	port := m.config.GmailServerPort

	addr := fmt.Sprintf("%s:%d", host, port)
	auth := smtp.PlainAuth("", from, password, host)

	msg := []byte(
		"To: " + to + "\r\n" +
			"From: " + from + "\r\n" +
			"Subject: " + subject + "\r\n" +
			"MIME-Version: 1.0\r\n" +
			"Content-Type: text/html; charset=\"UTF-8\"\r\n" +
			"\r\n" +
			body + "\r\n",
	)

	err := smtp.SendMail(addr, auth, from, []string{to}, msg)
	if err != nil {
		m.logger.Error("SMTP error", zap.Error(err))
	}
	return err
}
