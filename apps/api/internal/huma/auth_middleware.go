package huma

import (
	"github.com/coorpe-app/coorpe/internal/sessions"
	"github.com/danielgtaylor/huma/v2"
)

func NewAuthMiddleware(api huma.API, s *sessions.Sessions) func(
	ctx huma.Context,
	next func(huma.Context),
) {
	return func(ctx huma.Context, next func(huma.Context)) {
		isAuthorizationRequired := false
		for _, opScheme := range ctx.Operation().Security {
			var ok bool
			if _, ok = opScheme["authentication"]; ok {
				isAuthorizationRequired = true
				break
			}
		}

		if !isAuthorizationRequired {
			next(ctx)
			return
		}

		user, err := s.GetAuthenticatedUser(ctx.Context())
		if err != nil {
			huma.WriteErr(api, ctx, 401, "not authenticated", err)
			return
		}

		ctx = huma.WithValue(ctx, "user", user)

		next(ctx)
	}
}
