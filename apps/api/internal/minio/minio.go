package minio

import (
	"context"
	"fmt"
	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/fx"
	"go.uber.org/zap"
	"log/slog"
)

func New(logger logger.Logger, config config.Config, lc fx.Lifecycle) (*minio.Client, error) {
	logger.Info(
		"minio",
		zap.String("host", config.S3Host),
		zap.String("region", config.S3Region),
		zap.String("bucket", config.S3Bucket),
	)

	client, err := minio.New(
		config.S3Host,
		&minio.Options{
			Creds:  credentials.NewStaticV4(config.S3AccessToken, config.S3SecretToken, ""),
			Region: config.S3Region,
			Secure: true,
		},
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create minio client: %w", err)
	}

	lc.Append(
		fx.Hook{
			OnStart: func(ctx context.Context) error {
				err = client.MakeBucket(
					ctx,
					config.S3Bucket,
					minio.MakeBucketOptions{Region: config.S3Region},
				)

				if err != nil {
					exists, errBucketExists := client.BucketExists(ctx, config.S3Bucket)
					if errBucketExists != nil && !exists {
						return fmt.Errorf("failed to create bucket: %w", err)
					}
				} else {
					slog.Info("bucket already exists")
				}

				return nil
			},
			OnStop: nil,
		},
	)
	return client, nil
}
