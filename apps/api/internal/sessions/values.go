package sessions

import (
	"context"
	"fmt"
	"github.com/coorpe-app/coorpe/internal/entity"
)

func (s *Sessions) GetAuthenticatedUser(ctx context.Context) (entity.User, error) {
	val := s.SessionManager.Get(ctx, "userID")
	userID, ok := val.(string)
	if !ok || userID == "" {
		return entity.Nil, fmt.Errorf("cannot get user id from session")
	}

	user := entity.User{}
	if err := s.db.Table("users").First(&user, "id = ?", userID).Error; err != nil {
		return entity.Nil, fmt.Errorf("cannot get user from db: %w", err)
	}

	return user, nil
}

func (s *Sessions) GetCurrentWorkspace(ctx context.Context) (string, error) {
	currentWorkspaceId, ok := s.SessionManager.Get(ctx, "workspaceId").(string)
	if !ok {
		return "", fmt.<PERSON><PERSON><PERSON>("cannot get workspaceId from context")
	}

	return currentWorkspaceId, nil
}

func (s *Sessions) SetSessionCurrentWorkspace(ctx context.Context, workspaceId string) error {
	s.SessionManager.Put(ctx, "workspaceId", workspaceId)
	s.SessionManager.Commit(ctx)

	return nil
}
