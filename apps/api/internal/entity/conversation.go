package entity

import (
	"github.com/google/uuid"
)

type Conversation struct {
	ID          uuid.UUID `gorm:"column:id"`
	MemberOneID uuid.UUID `gorm:"column:member_one_id"`
	MemberTwoID uuid.UUID `gorm:"column:member_two_id"`

	WorkspaceID string    `gorm:"column:workspace_id"`
	Workspace   Workspace `gorm:"foreignKey:WorkspaceID"`

	MemberOne User       `gorm:"foreignKey:MemberOneID"`
	MemberTwo User       `gorm:"foreignKey:MemberTwoID"`
	Messages  []*Message `gorm:"foreignKey:ConversationID"`
}

var ConversationNil = Conversation{}
