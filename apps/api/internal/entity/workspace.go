package entity

import (
	"time"
)

type Workspace struct {
	ID          string            `gorm:"column:id"`
	Name        string            `gorm:"column:name"`
	IconURL     *string           `gorm:"column:icon_url"`
	Description *string           `gorm:"column:description"`
	Type        WorkspaceTypeEnum `gorm:"column:type"`
	CreatedAt   time.Time         `gorm:"column:created_at"`
}

type WorkspaceTypeEnum string

func (w WorkspaceTypeEnum) String() string {
	return string(w)
}

const (
	WorkspaceTypeScrum  WorkspaceTypeEnum = "SCRUM"
	WorkspaceTypeKanban WorkspaceTypeEnum = "KANBAN"
)

var WorkspaceNil = Workspace{}
