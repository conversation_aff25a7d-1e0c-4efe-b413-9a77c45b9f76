package entity

import (
	"github.com/google/uuid"
	"time"
)

type WorkspaceMember struct {
	UserID uuid.UUID `gorm:"column:user_id"`
	User   User      `gorm:"foreignKey:UserID"`

	WorkspaceID string    `gorm:"column:workspace_id"`
	Workspace   Workspace `gorm:"foreignKey:WorkspaceID"`

	Role       WorkspaceMemberRole `gorm:"column:role"`
	LastActive time.Time           `gorm:"column:last_active"`

	InvitedBy uuid.UUID `gorm:"column:invited_by"`
	Inviter   User      `gorm:"foreignKey:InvitedBy"`
}

type WorkspaceMemberRole string

func (w WorkspaceMemberRole) String() string {
	return string(w)
}

const (
	WorkspaceMemberRoleAdmin  WorkspaceMemberRole = "ADMIN"
	WorkspaceMemberRoleOwner  WorkspaceMemberRole = "OWNER"
	WorkspaceMemberRoleMember WorkspaceMemberRole = "MEMBER"
)

var WorkspaceMemberRoleNil = WorkspaceMemberRole("")
var WorkspaceMemberNil = WorkspaceMember{}
