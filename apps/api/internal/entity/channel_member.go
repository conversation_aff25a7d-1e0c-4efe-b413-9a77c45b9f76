package entity

import (
	"github.com/google/uuid"
	"time"
)

type ChannelMember struct {
	IsMuted    bool       `gorm:"column:is_muted;type:BOOL;default:false"`
	LastActive *time.Time `gorm:"column:last_active"`

	ChannelID string    `gorm:"column:channel_id;type:uuid;primaryKey;index:idx_channel_user,unique"`
	UserID    uuid.UUID `gorm:"column:user_id;type:uuid;primaryKey;index:idx_channel_user,unique"`

	User    User    `gorm:"foreignKey:UserID"`
	Channel Channel `gorm:"foreignKey:ChannelID"`
}

var ChannelMemberNil = ChannelMember{}
