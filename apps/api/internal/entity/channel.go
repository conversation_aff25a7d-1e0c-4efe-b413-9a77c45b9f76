package entity

import "time"

type Channel struct {
	ID          string  `gorm:"column:id"`
	Name        string  `gorm:"column:name"`
	Description *string `gorm:"column:description"`
	IsPrivate   bool    `gorm:"column:is_private"`

	WorkspaceID string    `gorm:"column:workspace_id"`
	Workspace   Workspace `gorm:"foreignKey:WorkspaceID"`

	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

var ChannelNil = Channel{}
