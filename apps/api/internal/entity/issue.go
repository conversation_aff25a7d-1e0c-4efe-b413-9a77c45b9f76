package entity

import (
	"github.com/google/uuid"
	"time"
)

type WorkspaceIssue struct {
	ID          string
	Title       string
	Description *string
	Type        WorkspaceIssueTypeEnum
	Status      WorkspaceIssueStatusEnum
	ParentID    *string
	SprintID    *string
	WorkspaceID string
	AssigneeID  *uuid.UUID

	CreatedAt time.Time
	UpdatedAt time.Time
}

var WorkspaceIssueNil = WorkspaceIssue{}

type WorkspaceIssueTypeEnum string

func (c WorkspaceIssueTypeEnum) String() string {
	return string(c)
}

const (
	WorkspaceIssueTypeUserStory WorkspaceIssueTypeEnum = "USER_STORY"
	WorkspaceIssueTypeBug       WorkspaceIssueTypeEnum = "BUG"
	WorkspaceIssueTypeTask      WorkspaceIssueTypeEnum = "TASK"
)

var WorkspaceIssueTypeNil = WorkspaceIssueTypeEnum("")

type WorkspaceIssueStatusEnum string

func (c WorkspaceIssueStatusEnum) String() string {
	return string(c)
}

const (
	WorkspaceIssueStatusTypeOpen       WorkspaceIssueStatusEnum = "OPEN"
	WorkspaceIssueStatusTypeInProgress WorkspaceIssueStatusEnum = "IN_PROGRESS"
	WorkspaceIssueStatusTypeCompleted  WorkspaceIssueStatusEnum = "COMPLETED"
	WorkspaceIssueStatusTypeCancelled  WorkspaceIssueStatusEnum = "CANCELLED"
)

var WorkspaceIssueStatusNil = WorkspaceIssueStatusEnum("")
