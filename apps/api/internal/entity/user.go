package entity

import (
	"time"

	"github.com/google/uuid"
)

type User struct {
	ID uuid.UUID `gorm:"column:id"`

	Password      string    `gorm:"column:password"`
	Name          string    `gorm:"column:name"`
	Birthday      time.Time `gorm:"column:birthday"`
	Email         string    `gorm:"column:email"`
	EmailVerified bool      `gorm:"column:email_verified"`
	AvatarURL     string    `gorm:"column:avatar_url"`

	CreatedAt time.Time `gorm:"type:timestamp with time zone" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp with time zone" json:"updated_at"`
}

var Nil = User{}
