package entity

import (
	"database/sql"
	"github.com/google/uuid"
	"time"
)

type Message struct {
	ID       uuid.UUID              `gorm:"column:id;primaryKey;type:uuid;default:gen_random_uuid()"`
	Body     string                 `gorm:"column:body;not null;type:text"`
	Type     string                 `gorm:"column:type;not null;type:varchar(50)"`
	Metadata map[string]interface{} `gorm:"column:metadata;serializer:json"`

	WorkspaceID    string    `gorm:"column:workspace_id;type:uuid;index;not null"`
	ChannelID      *string   `gorm:"column:channel_id;type:uuid;index"`
	ConversationID *string   `gorm:"column:conversation_id;type:uuid;index"`
	SenderID       uuid.UUID `gorm:"column:sender_id;type:uuid;index;not null"`

	Workspace    Workspace     `gorm:"foreignKey:WorkspaceID"`
	Channel      *Channel      `gorm:"foreignKey:ChannelID"`
	Conversation *Conversation `gorm:"foreignKey:ConversationID"`
	Sender       User          `gorm:"foreignKey:SenderID"`

	CreatedAt time.Time    `gorm:"type:timestamp with time zone"`
	UpdatedAt time.Time    `gorm:"type:timestamp with time zone"`
	DeletedAt sql.NullTime `gorm:"index"`
}

var MessageNil = Message{}
