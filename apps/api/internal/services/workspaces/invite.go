package workspaces

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/coorpe-app/coorpe/internal/entity"
	"math/rand"
	"time"

	"github.com/coorpe-app/coorpe/internal/mailer"
	"github.com/google/uuid"
	"github.com/samber/lo"
)

type Invite struct {
	Sent     []string `json:"sent"`
	Failed   []string `json:"failed"`
	Existing []string `json:"existing"`
}

type SendInviteInput struct {
	WorkspaceID string    `json:"workspace_id"`
	Emails      []string  `json:"emails"`
	UserID      uuid.UUID `json:"user_id"`
	UserName    string    `json:"username"`
}

func (s *Service) SendInvite(c context.Context, input SendInviteInput) (*Invite, error) {
	found, err := s.workspaceRepo.GetByID(c, input.WorkspaceID)
	if err != nil {
		return nil, err
	}

	role, err := s.workspaceMembersRepo.GetRole(c, input.WorkspaceID, input.UserID)
	if err != nil || !lo.Contains(
		[]entity.WorkspaceMemberRole{entity.WorkspaceMemberRoleOwner, entity.WorkspaceMemberRoleAdmin},
		entity.WorkspaceMemberRole(role),
	) {
		return nil, err
	}

	response := &Invite{
		Sent:     make([]string, 0),
		Failed:   make([]string, 0),
		Existing: make([]string, 0),
	}

	for _, email := range input.Emails {
		existingMember, err := s.workspaceMembersRepo.GetUserIDByEmail(c, input.WorkspaceID, email)
		if err == nil && existingMember != "" {
			response.Existing = append(response.Existing, email)
			continue
		}

		tokenBytes := make([]byte, 32)
		if _, err := rand.Read(tokenBytes); err != nil {
			response.Failed = append(response.Failed, email)
			continue
		}
		token := base64.URLEncoding.EncodeToString(tokenBytes)

		inviteData := map[string]string{
			"email":        email,
			"workspace_id": input.WorkspaceID,
			"invited_by":   input.UserID.String(),
		}

		key := fmt.Sprintf("invite:found:%s", token)
		if err := s.redis.HSet(c, key, inviteData).Err(); err != nil {
			response.Failed = append(response.Failed, email)
			continue
		}
		s.redis.Expire(c, key, 8*time.Hour)

		invite := mailer.InviteToWorkspace{
			WorkspaceName: found.Name,
			InviterName:   input.UserName,
			Token:         token,
		}

		if err := s.mailer.SendInviteToWorkspace(email, invite); err != nil {
			response.Failed = append(response.Failed, email)
			continue
		}

		response.Sent = append(response.Sent, email)
	}

	return response, nil
}
