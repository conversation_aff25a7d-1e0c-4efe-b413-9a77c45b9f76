package workspaces

import (
	"context"
	"fmt"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"log/slog"
	"path/filepath"
	"strings"
)

func computeWorkspaceIconFileName(file entity.File, fileID uuid.UUID) (string, error) {
	fileExtension := filepath.Ext(file.Filename)
	if fileExtension == "" {
		return "", fmt.Errorf("file has no extension: %s", file.Filename)
	}
	if !strings.HasPrefix(file.ContentType, "image/") {
		return "", fmt.Errorf("unsupported file type: %s", file.ContentType)
	}

	fileExtension = strings.ToLower(fileExtension)
	fileName := fmt.Sprintf("%s%s", fileID, fileExtension)

	return fileName, nil
}

func (s *Service) computeWorkspaceIconUrl(fileName string) string {
	return s.config.S3PublicUrl + "/workspace_icons/" + fileName
}

func (s *Service) CreateIcon(c context.Context, file entity.File) (string, error) {
	fileID := uuid.New()
	fileName, err := computeWorkspaceIconFileName(file, fileID)
	if err != nil {
		return "", fmt.Errorf("failed to compute file name: %w", err)
	}

	uploadInfo, err := s.minioClient.PutObject(
		c,
		s.config.S3Bucket,
		fmt.Sprintf("workspace_icons/%s", fileName),
		file.Content,
		file.Size,
		minio.PutObjectOptions{
			ContentType: file.ContentType,
		},
	)
	s.Logger.Info("File uploaded", slog.Any("uploadInfo", uploadInfo))

	if err != nil {
		s.Logger.Error("Failed to upload file", slog.String("fileName", fileName), slog.Any("error", err))
		return "", fmt.Errorf("failed to upload file: %w", err)
	}

	return s.computeWorkspaceIconUrl(fileName), nil
}

func (s *Service) DeleteIcon(c *gin.Context, fileName string) error {
	if err := s.minioClient.RemoveObject(
		c,
		s.config.S3Bucket,
		fmt.Sprintf("workspace_icons/%s", fileName),
		minio.RemoveObjectOptions{},
	); err != nil {
		return fmt.Errorf("failed to remove file: %w", err)
	}

	return nil
}

//func (s *Service) UpdateIcon(c *gin.Context, id uuid.UUID, file entity.File) (string, error) {
//	fileName, err := s.computeWorkspaceIconFileName(file, id)
//	if err != nil {
//		return "", fmt.Errorf("failed to compute file name: %w", err)
//	}
//
//	if file.Filename != input.Filename {
//		if err := s.minioClient.RemoveObject(
//			c,
//			s.database.S3Bucket,
//			fmt.Sprintf("workspace_icons/%s", input.Filename),
//			minio.RemoveObjectOptions{},
//		); err != nil {
//			return "", fmt.Errorf("failed to remove old file: %w", err)
//		}
//	}
//
//	_, err = s.minioClient.PutObject(
//		c,
//		s.database.S3Bucket,
//		fmt.Sprintf("workspace_icons/%s", fileName),
//		file.File,
//		file.Size,
//		minio.PutObjectOptions{
//			ContentType: file.ContentType,
//		},
//	)
//	if err != nil {
//		return "", fmt.Errorf("failed to upload file: %w", err)
//	}
//
//	return fileName, nil
//}
