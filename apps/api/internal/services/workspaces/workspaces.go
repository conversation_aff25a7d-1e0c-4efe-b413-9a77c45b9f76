package workspaces

import (
	"context"
	"errors"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/internal/mailer"
	"github.com/coorpe-app/coorpe/libs/config"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces/model"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces_members"
	model2 "github.com/coorpe-app/coorpe/libs/repositories/workspaces_members/model"
	"github.com/google/uuid"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"github.com/minio/minio-go/v7"
	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
	"gorm.io/gorm"
	"log/slog"
	"time"
)

type Params struct {
	fx.In

	DB                   *gorm.DB
	Logger               logger.Logger
	Redis                *redis.Client
	Mailer               *mailer.Mailer
	WorkspaceRepo        workspaces.Repository
	WorkspaceMembersRepo workspaces_members.Repository
	MinioClient          *minio.Client
	Config               config.Config
}

func New(p Params) *Service {
	return &Service{
		DB:                   p.DB,
		Logger:               p.Logger,
		redis:                p.Redis,
		mailer:               p.Mailer,
		workspaceRepo:        p.WorkspaceRepo,
		workspaceMembersRepo: p.WorkspaceMembersRepo,
		minioClient:          p.MinioClient,
		config:               p.Config,
	}
}

type Service struct {
	DB *gorm.DB

	Logger               logger.Logger
	redis                *redis.Client
	mailer               *mailer.Mailer
	workspaceRepo        workspaces.Repository
	workspaceMembersRepo workspaces_members.Repository
	minioClient          *minio.Client
	config               config.Config
}

func (s *Service) modelToEntity(m model.Workspace) entity.Workspace {
	return entity.Workspace{
		ID:          m.ID,
		Name:        m.Name,
		Description: m.Description,
		IconURL:     m.IconURL,
		Type:        entity.WorkspaceTypeEnum(m.Type),
	}
}

type CreateInput struct {
	Name        string
	Description *string
	IconURL     *string
	Type        entity.WorkspaceTypeEnum
}

func (s *Service) generateID() string {
	return gonanoid.Must(10)
}

func (s *Service) Create(ctx context.Context, input CreateInput) (entity.Workspace, error) {
	var dbWorkspace model.Workspace

	userID := ctx.Value("user").(entity.User).ID
	if userID == uuid.Nil {
		return entity.WorkspaceNil, errors.New("usuário não encontrado")
	}

	err := s.DB.Transaction(
		func(tx *gorm.DB) error {
			newWorkspace, err := s.workspaceRepo.Create(
				tx, workspaces.CreateInput{
					ID:          s.generateID(),
					Name:        input.Name,
					Description: input.Description,
					IconURL:     input.IconURL,
					Type:        model.WorkspaceTypeEnum(input.Type),
				},
			)
			if err != nil {
				return err
			}

			if err = s.workspaceMembersRepo.Create(
				tx, workspaces_members.CreateInput{
					WorkspaceID: newWorkspace.ID,
					UserID:      userID,
					Role:        model2.Owner,
					InvitedBy:   userID,
					LastActive:  time.Now(),
				},
			); err != nil {
				return err
			}

			dbWorkspace = newWorkspace

			return nil
		},
	)
	if err != nil {
		s.Logger.Error("failed to create workspaces", slog.Any("err", err))
		return entity.Workspace{}, err
	}

	return s.modelToEntity(dbWorkspace), nil
}

type UpdateInput struct {
	Name        string
	Description *string
	IconURL     *string
}

func (s *Service) Update(ctx context.Context, id string, input UpdateInput) (entity.Workspace, error) {
	dbWorkspace, err := s.workspaceRepo.GetByID(ctx, id)
	if err != nil {
		return entity.WorkspaceNil, err
	}

	if err := s.workspaceRepo.Update(
		ctx,
		dbWorkspace.ID,
		workspaces.UpdateInput{
			Name:        input.Name,
			Description: input.Description,
			IconURL:     input.IconURL,
		},
	); err != nil {
		s.Logger.Error("failed to update workspaces", slog.Any("err", err))
		return entity.WorkspaceNil, err
	}

	return s.modelToEntity(dbWorkspace), nil
}

type GetManyInput struct {
	UserID uuid.UUID
	Page   int
	Limit  int
}

type GetManyOutput struct {
	Items []model.Workspace
	Total int64
}

func (s *Service) GetByID(ctx context.Context, id string) (entity.Workspace, error) {
	dbWorkspace, err := s.workspaceRepo.GetByID(ctx, id)
	if err != nil {
		s.Logger.Error("failed to get workspaces", slog.Any("err", err))
		return entity.WorkspaceNil, err
	}

	return s.modelToEntity(dbWorkspace), nil
}

func (s *Service) GetMany(ctx context.Context, input GetManyInput) (GetManyOutput, error) {
	result, err := s.workspaceRepo.GetMany(
		ctx,
		workspaces.GetManyInput{
			UserID: input.UserID,
			Page:   input.Page,
			Limit:  input.Limit,
		},
	)

	if err != nil {
		s.Logger.Error("failed to get workspaces", slog.Any("err", err))
		return GetManyOutput{}, err
	}

	output := GetManyOutput{
		Items: make([]model.Workspace, 0, len(result.Items)),
		Total: result.Total,
	}

	for _, w := range result.Items {
		output.Items = append(output.Items, w)
	}

	return output, nil
}
