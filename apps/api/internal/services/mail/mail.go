package mail

import (
	"context"
	"crypto/rand"
	"fmt"
	"github.com/coorpe-app/coorpe/libs/logger"
	"go.uber.org/fx"
	"time"

	"github.com/coorpe-app/coorpe/internal/mailer"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Params struct {
	fx.In

	Logger logger.Logger
	DB     *gorm.DB
	Redis  *redis.Client
	Mailer *mailer.Mailer
}

func New(p Params) *Service {
	return &Service{
		DB:     p.DB,
		Redis:  p.Redis,
		mailer: p.Mailer,
		logger: p.<PERSON>gger,
	}
}

type Service struct {
	DB     *gorm.DB
	Redis  *redis.Client
	mailer *mailer.Mailer
	logger logger.Logger
}

type EmailRedis struct {
	Code  string `redis:"code"`
	Email string `redis:"email"`
}

func (s *Service) SendEmailCode(ctx context.Context, email string) error {
	verificationCode := s.GenerateVerificationCode()
	keyID := uuid.New().String()
	key := fmt.Sprintf("email_code_%s", keyID)

	verificationData := EmailRedis{
		Code:  verificationCode,
		Email: email,
	}

	err := s.Redis.HSet(ctx, key, verificationData).Err()
	if err != nil {
		return err
	}

	s.Redis.Expire(ctx, key, 15*time.Minute)

	err = s.mailer.SendEmailCode(email, verificationCode)
	if err != nil {
		return fmt.Errorf("falha ao enviar email de confirmação: %w", err)
	}

	return nil
}

func (s *Service) VerifyEmailCode(ctx context.Context, code string) error {
	if len(code) != 6 {
		return fmt.Errorf("código de verificação inválido")
	}

	keys, err := s.Redis.Keys(ctx, "email_code_*").Result()
	if err != nil {
		return fmt.Errorf("falha ao verificar código: %w", err)
	}

	for _, key := range keys {
		var verificationData EmailRedis
		err := s.Redis.HGetAll(ctx, key).Scan(&verificationData)
		if err != nil {
			continue
		}

		if verificationData.Code == code {
			if err := s.Redis.Del(ctx, key).Err(); err != nil {
				s.logger.Error("failed to delete verification code", zap.Error(err))
			}

			return nil
		}
	}

	return fmt.Errorf("código de verificação inválido")
}

func (s *Service) GenerateVerificationCode() string {
	const digits = "0123456789"
	bytes := make([]byte, 6)
	_, err := rand.Read(bytes)
	if err != nil {
		return ""
	}

	for i := 0; i < 6; i++ {
		bytes[i] = digits[int(bytes[i])%len(digits)]
	}
	return string(bytes)
}
