package workspaces_members

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces_members"
	"github.com/coorpe-app/coorpe/libs/repositories/workspaces_members/model"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"time"
)

type Service struct {
	DB *gorm.DB

	repository workspaces_members.Repository
	redis      *redis.Client
}

func New(
	DB *gorm.DB,
	repository workspaces_members.Repository,
	redis *redis.Client,
) *Service {
	return &Service{
		DB:         DB,
		repository: repository,
		redis:      redis,
	}
}

func (s *Service) modelToEntity(m model.WorkspaceMember) entity.WorkspaceMember {
	return entity.WorkspaceMember{
		Role:       entity.WorkspaceMemberRole(m.Role),
		LastActive: m.LastActive,
		InvitedBy:  m.InvitedBy,
		User: entity.User{
			ID:        m.UserID,
			Name:      m.Name,
			AvatarURL: m.AvatarURL,
			Email:     m.Email,
		},
	}
}

type CreateInput struct {
	WorkspaceID string
	UserID      uuid.UUID
	Role        entity.WorkspaceMemberRole
	InvitedBy   uuid.UUID
	LastActive  time.Time
}

func (s *Service) GetRole(ctx context.Context, workspaceID string, userID uuid.UUID) (entity.WorkspaceMemberRole, error) {
	role, err := s.repository.GetRole(ctx, workspaceID, userID)
	if err != nil {
		return entity.WorkspaceMemberRoleNil, err
	}

	return entity.WorkspaceMemberRole(role), nil
}

func (s *Service) GetUserIDByEmail(ctx context.Context, workspaceID string, email string) (string, error) {
	return s.repository.GetUserIDByEmail(ctx, workspaceID, email)
}

func (s *Service) GetMany(ctx context.Context, workspaceID string) ([]entity.WorkspaceMember, error) {
	members, err := s.repository.GetMany(ctx, workspaceID)
	if err != nil {
		return nil, err
	}

	entities := make([]entity.WorkspaceMember, len(members))
	for i, m := range members {
		entities[i] = s.modelToEntity(m)
	}

	return entities, nil
}
