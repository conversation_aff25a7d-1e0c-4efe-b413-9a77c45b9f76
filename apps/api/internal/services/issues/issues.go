package issues

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/bus"
	workspace_issue_bus "github.com/coorpe-app/coorpe/libs/bus/issues"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/issues"
	"github.com/coorpe-app/coorpe/libs/repositories/issues/model"
	"github.com/coorpe-app/coorpe/pkg/typeid"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"golang.org/x/exp/slog"
)

type Params struct {
	fx.In

	Bus    *bus.Bus
	Logger logger.Logger
	Repo   issues.Repository
}

func New(p Params) *Service {
	return &Service{
		bus:    p.Bus,
		repo:   p.Repo,
		logger: p.Logger,
	}
}

type Service struct {
	bus    *bus.Bus
	repo   issues.Repository
	logger logger.Logger
}

func (s *Service) modelToEntity(task model.WorkspaceIssue) entity.WorkspaceIssue {
	return entity.WorkspaceIssue{
		ID:          task.ID,
		Title:       task.Title,
		Description: task.Description,
		Status:      entity.WorkspaceIssueStatusEnum(task.Status.String()),
		Type:        entity.WorkspaceIssueTypeEnum(task.Type.String()),
		AssigneeID:  task.AssigneeID,
		WorkspaceID: task.WorkspaceID,
		SprintID:    task.SprintID,
		ParentID:    task.ParentID,
		CreatedAt:   task.CreatedAt,
		UpdatedAt:   task.UpdatedAt,
	}
}

func (s *Service) GetMany(ctx context.Context, columnID string) ([]entity.WorkspaceIssue, error) {
	dbTasks, err := s.repo.GetMany(ctx, columnID)
	if err != nil {
		s.logger.Error("cannot get kanban issues", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.WorkspaceIssue
	for _, task := range dbTasks {
		entities = append(entities, s.modelToEntity(task))
	}
	return entities, nil
}

func (s *Service) GetByColumnID(ctx context.Context, columnID string) ([]entity.WorkspaceIssue, error) {
	dbTasks, err := s.repo.GetByColumnID(ctx, columnID)
	if err != nil {
		s.logger.Error("cannot get kanban issues", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.WorkspaceIssue
	for _, task := range dbTasks {
		entities = append(entities, s.modelToEntity(task))
	}
	return entities, nil
}

func (s *Service) GetBacklogByWorkspaceID(ctx context.Context, workspaceID string) ([]entity.WorkspaceIssue, error) {
	backlogTasks, err := s.repo.GetBacklogByWorkspaceID(ctx, workspaceID)
	if err != nil {
		s.logger.Error("failed to get backlog issues", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.WorkspaceIssue
	for _, task := range backlogTasks {
		entities = append(entities, s.modelToEntity(task))
	}
	return entities, nil
}

func (s *Service) GetByID(ctx context.Context, id string) (entity.WorkspaceIssue, error) {
	task, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("cannot get kanban task", slog.Any("error", err))
		return entity.WorkspaceIssueNil, err
	}

	return s.modelToEntity(task), nil
}

type CreateInput struct {
	Title       string
	Description *string
	ParentID    *string
	SprintID    *string
	WorkspaceID string
	Status      entity.WorkspaceIssueStatusEnum
	Type        entity.WorkspaceIssueTypeEnum
	AssigneeID  *uuid.UUID
}

func (s *Service) Create(ctx context.Context, input CreateInput) (entity.WorkspaceIssue, error) {
	taskID := typeid.GenerateID("task")

	createdTask, err := s.repo.Create(
		ctx,
		issues.CreateInput{
			ID:          taskID,
			Title:       input.Title,
			Description: input.Description,
			AssigneeID:  input.AssigneeID,
			ParentID:    input.ParentID,
			SprintID:    input.SprintID,
			WorkspaceID: input.WorkspaceID,
			Status:      model.IssueStatusEnum(input.Status),
			Type:        model.IssueTypeEnum(input.Type),
		},
	)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return entity.WorkspaceIssueNil, err
	}

	return s.modelToEntity(createdTask), nil
}

type UpdateInput struct {
	Title       *string
	Description *string
	ParentID    *string
	SprintID    *string
	Status      *entity.WorkspaceIssueStatusEnum
	Type        *entity.WorkspaceIssueTypeEnum
	AssigneeID  *uuid.UUID
}

func (s *Service) Update(ctx context.Context, id string, input UpdateInput) (entity.WorkspaceIssue, error) {
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("cannot get kanban task", slog.Any("error", err))
		return entity.WorkspaceIssueNil, err
	}

	updatedTask, err := s.repo.Update(
		ctx, id, issues.UpdateInput{
			Title:       input.Title,
			Description: input.Description,
			Status:      (*model.IssueStatusEnum)(input.Status),
			Type:        (*model.IssueTypeEnum)(input.Type),
			AssigneeID:  input.AssigneeID,
			ParentID:    input.ParentID,
			SprintID:    input.SprintID,
		},
	)
	if err != nil {
		s.logger.Error("cannot update kanban task", slog.Any("error", err))
		return entity.WorkspaceIssueNil, err
	}

	if err := s.bus.Scrum.UpdateIssue.Publish(
		workspace_issue_bus.Issue{
			Title:       updatedTask.Title,
			Description: updatedTask.Description,
			Status:      updatedTask.Status.String(),
			Type:        updatedTask.Type.String(),
			WorkspaceID: updatedTask.WorkspaceID,
			SprintID:    updatedTask.SprintID,
			ParentID:    updatedTask.ParentID,
			AssigneeID:  updatedTask.AssigneeID,
			CreatedAt:   updatedTask.CreatedAt,
			UpdatedAt:   updatedTask.UpdatedAt,
		},
	); err != nil {
		s.logger.Error("cannot publish kanban task updated", slog.Any("error", err))
		return entity.WorkspaceIssueNil, err
	}

	return s.modelToEntity(updatedTask), nil
}

func (s *Service) Delete(ctx context.Context, id string) error {
	err := s.repo.Delete(ctx, id)
	if err != nil {
		s.logger.Error("cannot delete kanban task", slog.Any("error", err))
		return err
	}

	return nil
}
