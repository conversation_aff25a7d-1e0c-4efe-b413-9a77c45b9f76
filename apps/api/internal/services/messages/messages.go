package messages

import (
	"context"
	"errors"
	natsrouter "github.com/coorpe-app/coorpe/internal/delivery/nats-router"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/bus"
	chatmessages "github.com/coorpe-app/coorpe/libs/bus/channel-messages"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/channels"
	"github.com/coorpe-app/coorpe/libs/repositories/message"
	"github.com/coorpe-app/coorpe/libs/repositories/message/model"
	"github.com/goccy/go-json"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"log/slog"
	"sync"
)

type Params struct {
	fx.In
	LC fx.Lifecycle

	Logger      logger.Logger
	MessageRepo message.Repository
	ChannelRepo channels.Repository
	CoorpeBus   *bus.Bus

	Nats natsrouter.Router
}

const (
	channelMessagesSubscriptionKey    = "api.channelMessages"
	channelMessagesSubscriptionKeyAll = channelMessagesSubscriptionKey + ".All"
)

func channelMessagesSubscriptionKeyCreate(channelId string) string {
	return channelMessagesSubscriptionKey + "." + channelId
}

func New(p Params) *Service {
	s := &Service{
		messageRepo: p.MessageRepo,
		channelRepo: p.ChannelRepo,
		nats:        p.Nats,
		coorpeBus:   p.CoorpeBus,
		Logger:      p.Logger,
		chanSubs:    make(map[string]struct{}),
	}

	p.LC.Append(
		fx.Hook{
			OnStart: func(ctx context.Context) error {
				return p.CoorpeBus.ChatMessages.Subscribe(s.handleBusEvent)
			},
			OnStop: func(ctx context.Context) error {
				p.CoorpeBus.ChatMessages.Unsubscribe()
				return nil
			},
		},
	)

	return s
}

type Service struct {
	messageRepo message.Repository
	channelRepo channels.Repository
	chanSubs    map[string]struct{}
	chanSubsMu  sync.RWMutex
	coorpeBus   *bus.Bus
	Logger      logger.Logger

	nats natsrouter.Router
}

func (s *Service) modelToEntity(m model.Message) entity.Message {
	return entity.Message{
		ID:          m.ID,
		Body:        m.Body,
		WorkspaceID: m.WorkspaceID,
		ChannelID:   m.ChannelID,
		Type:        m.Type,
		Sender: entity.User{
			ID:        m.SenderID,
			Name:      m.SenderName,
			AvatarURL: m.SenderAvatar,
		},
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

func (s *Service) handleBusEvent(_ context.Context, messages chatmessages.ChatMessages) struct{} {
	channelID := ""
	if messages.ChannelID != nil {
		channelID = *messages.ChannelID
	}

	msg := model.Message{
		ID:             uuid.New(),
		Body:           messages.Body,
		WorkspaceID:    messages.WorkspaceID,
		ChannelID:      messages.ChannelID,
		ConversationID: messages.ConversationID,
		SenderID:       messages.SenderID,
		SenderName:     messages.SenderName,
		SenderAvatar:   messages.SenderAvatar,
		CreatedAt:      messages.CreatedAt,
		UpdatedAt:      messages.UpdatedAt,
		Type:           messages.Type,
	}

	s.chanSubsMu.RLock()
	if _, ok := s.chanSubs[channelID]; ok {
		err := s.nats.Publish(channelMessagesSubscriptionKeyCreate(channelID), msg)
		if err != nil {
			s.Logger.Error("Error publishing messages to NATS", slog.Any("err", err))
		}
	}
	s.chanSubsMu.RUnlock()

	err := s.nats.Publish(channelMessagesSubscriptionKeyAll, msg)
	if err != nil {
		s.Logger.Error("Error publishing messages to NATS: ", slog.Any("err", err), slog.String("channelID", channelID))
	}

	return struct{}{}
}

func (s *Service) SubscribeToNewMessagesByChannelID(ctx context.Context, channelID string) <-chan chatmessages.ChatMessages {
	s.chanSubsMu.Lock()
	s.chanSubs[channelID] = struct{}{}
	s.chanSubsMu.Unlock()

	msgChannel := make(chan chatmessages.ChatMessages)

	go func() {
		sub, err := s.nats.Subscribe(
			[]string{
				channelMessagesSubscriptionKeyCreate(channelID),
			},
		)
		if err != nil {
			panic(err)
		}
		defer func() {
			s.chanSubsMu.Lock()
			delete(s.chanSubs, channelID)
			s.chanSubsMu.Unlock()
			sub.Unsubscribe()
			close(msgChannel)
		}()

		for {
			select {
			case <-ctx.Done():
				return
			case messages, ok := <-sub.GetChannel():
				if !ok {
					return
				}

				var msg chatmessages.ChatMessages
				if err := json.Unmarshal(messages, &msg); err != nil {
					panic(err)
				}

				msgChannel <- msg
			}
		}
	}()

	return msgChannel
}

type SendMessageInput struct {
	Body           string
	WorkspaceID    string
	ChannelID      *string
	ConversationID *string
	Type           string
}

func (s *Service) Create(c context.Context, input SendMessageInput) (entity.Message, error) {
	userID := c.Value("user").(entity.User).ID
	if userID == uuid.Nil {
		return entity.MessageNil, errors.New("usuário não encontrado")
	}

	count, err := s.channelRepo.CountByID(*input.ChannelID)
	if err != nil || count < 1 {
		return entity.MessageNil, errors.New("canal não encontrado")
	}

	newMessage, err := s.messageRepo.Create(
		message.CreateInput{
			Body:        input.Body,
			WorkspaceID: input.WorkspaceID,
			ChannelID:   input.ChannelID,
			Type:        input.Type,
			SenderID:    userID,
		},
	)
	if err != nil {
		return entity.MessageNil, err
	}

	if err := s.coorpeBus.ChatMessages.Publish(
		chatmessages.ChatMessages{
			ID:             newMessage.ID,
			Body:           newMessage.Body,
			WorkspaceID:    newMessage.WorkspaceID,
			Type:           newMessage.Type,
			ChannelID:      newMessage.ChannelID,
			ConversationID: newMessage.ConversationID,
			SenderID:       newMessage.SenderID,
			SenderName:     newMessage.SenderName,
			SenderAvatar:   newMessage.SenderAvatar,
			CreatedAt:      newMessage.CreatedAt,
			UpdatedAt:      newMessage.UpdatedAt,
		},
	); err != nil {
		s.Logger.Error("Error publishing messages to NATS", slog.Any("err", err))
	}

	return s.modelToEntity(newMessage), nil
}

type GetManyInput struct {
	ChannelID string
	Page      int
	Limit     int
}

type GetManyOutput struct {
	Items []entity.Message
	Total int64
}

func (s *Service) GetMany(input GetManyInput) (GetManyOutput, error) {
	messages, err := s.messageRepo.GetMany(
		message.GetManyInput{
			ChannelID: input.ChannelID,
			Page:      input.Page,
			Limit:     input.Limit,
		},
	)

	if err != nil {
		return GetManyOutput{}, err
	}

	output := GetManyOutput{
		Items: make([]entity.Message, 0, len(messages.Items)),
		Total: messages.Total,
	}

	for _, m := range messages.Items {
		output.Items = append(output.Items, s.modelToEntity(m))
	}

	return output, nil
}
