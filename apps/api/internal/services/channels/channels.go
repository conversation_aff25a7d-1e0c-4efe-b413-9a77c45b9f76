package channels

import (
	"context"
	"fmt"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/repositories/channels"
	"github.com/coorpe-app/coorpe/libs/repositories/channels/model"
	"github.com/coorpe-app/coorpe/libs/repositories/channels_members"
	"github.com/google/uuid"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

type Service struct {
	DB *gorm.DB

	channelRepo        channels.Repository
	channelMembersRepo channels_members.Repository
}

type Params struct {
	fx.In

	DB                 *gorm.DB
	ChannelRepo        channels.Repository
	ChannelMembersRepo channels_members.Repository
}

func New(p Params) *Service {
	return &Service{
		DB:                 p.DB,
		channelRepo:        p.ChannelRepo,
		channelMembersRepo: p.ChannelMembersRepo,
	}
}

func (s *Service) modelToEntity(m model.Channel) entity.Channel {
	return entity.Channel{
		ID:          m.ID,
		Name:        m.Name,
		Description: m.Description,
		IsPrivate:   m.IsPrivate,
		WorkspaceID: m.WorkspaceID,
	}
}

type CreateChannelInput struct {
	Name        string
	Description *string
	IsPrivate   bool
	WorkspaceID string
}

func (s *Service) generateID() string {
	return gonanoid.Must(10)
}

func (s *Service) Create(c context.Context, input CreateChannelInput) (entity.Channel, error) {
	var dbChannel model.Channel

	userID := c.Value("user").(entity.User).ID
	if userID == uuid.Nil {
		return entity.ChannelNil, fmt.Errorf("usuário não encontrado")
	}

	err := s.DB.Transaction(
		func(tx *gorm.DB) error {
			newChannel, err := s.channelRepo.Create(
				tx, &channels.CreateInput{
					ID:          s.generateID(),
					Name:        input.Name,
					Description: input.Description,
					IsPrivate:   input.IsPrivate,
					WorkspaceID: input.WorkspaceID,
				},
			)
			if err != nil {
				return err
			}

			if err = s.channelMembersRepo.Create(
				tx, channels_members.CreateInput{
					ChannelID: newChannel.ID,
					UserID:    userID,
				},
			); err != nil {
				return err
			}

			dbChannel = newChannel

			return nil
		},
	)
	if err != nil {
		return entity.ChannelNil, err
	}

	return s.modelToEntity(dbChannel), nil
}

type GetManyInput struct {
	Page        int
	Limit       int
	WorkspaceID string
	UserID      uuid.UUID
}

type GetManyOutput struct {
	Items []model.Channel
	Total int64
}

func (s *Service) GetMany(input GetManyInput) (GetManyOutput, error) {
	dbChannels, err := s.channelRepo.GetMany(
		channels.GetManyInput{
			Page:        input.Page,
			Limit:       input.Limit,
			WorkspaceID: input.WorkspaceID,
			UserID:      input.UserID,
		},
	)

	if err != nil {
		return GetManyOutput{}, err
	}

	output := GetManyOutput{
		Items: make([]model.Channel, 0, len(dbChannels.Items)),
		Total: dbChannels.Total,
	}

	for _, ch := range dbChannels.Items {
		output.Items = append(output.Items, ch)
	}

	return output, nil
}
