package boards

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/bus"
	kanban_boards_bus "github.com/coorpe-app/coorpe/libs/bus/boards"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/boards"
	"github.com/coorpe-app/coorpe/libs/repositories/boards/model"
	"github.com/coorpe-app/coorpe/pkg/typeid"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"log/slog"
)

type Service struct {
	repo   boards.Repository
	logger logger.Logger
	bus    *bus.Bus
}

type Params struct {
	fx.In

	Bus    *bus.Bus
	Logger logger.Logger
	Repo   boards.Repository
}

func New(p Params) *Service {
	return &Service{
		repo:   p.Repo,
		logger: p.<PERSON>,
		bus:    p.Bus,
	}
}

func (s *Service) modelToEntity(board model.WorkspaceBoard) entity.WorkspaceBoard {
	return entity.WorkspaceBoard{
		ID:          board.ID,
		Title:       board.Title,
		Description: board.Description,
		CreatedAt:   board.CreatedAt,
		UpdatedAt:   board.UpdatedAt,
	}
}

func (s *Service) GetMany(ctx context.Context, workspaceID string) ([]entity.WorkspaceBoard, error) {
	dbBoards, err := s.repo.GetMany(ctx, workspaceID)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.WorkspaceBoard
	for _, board := range dbBoards {
		entities = append(entities, s.modelToEntity(board))
	}

	return entities, nil
}

func (s *Service) GetByID(ctx context.Context, id string) (entity.WorkspaceBoard, error) {
	board, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return entity.WorkspaceBoard{}, err
	}

	return s.modelToEntity(board), nil
}

type CreateInput struct {
	Title       string
	Description *string
	WorkspaceID string
	OwnerID     uuid.UUID
}

func (s *Service) Create(ctx context.Context, input CreateInput) (entity.WorkspaceBoard, error) {
	modelBoard := model.WorkspaceBoard{
		ID:          typeid.GenerateID("board"),
		Title:       input.Title,
		Description: input.Description,
		OwnerID:     input.OwnerID,
		WorkspaceID: input.WorkspaceID,
	}

	createdBoard, err := s.repo.Create(ctx, modelBoard)
	if err != nil {
		s.logger.Error("create kanban board", slog.Any("error", err))
		return entity.WorkspaceBoard{}, err
	}

	return s.modelToEntity(createdBoard), nil
}

type UpdateInput struct {
	Title       string
	Description *string
}

func (s *Service) Update(ctx context.Context, id string, input UpdateInput) (entity.WorkspaceBoard, error) {
	board, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return entity.WorkspaceBoardNil, err
	}

	board.Title = input.Title
	board.Description = input.Description
	updatedBoard, err := s.repo.Update(ctx, board)
	if err != nil {
		s.logger.Error("cannot update kanban board", slog.Any("error", err))
		return entity.WorkspaceBoardNil, err
	}

	if err := s.bus.Scrum.UpdateBoard.Publish(
		kanban_boards_bus.Board{
			Title:       updatedBoard.Title,
			Description: updatedBoard.Description,
			OwnerID:     updatedBoard.OwnerID,
			UpdatedAt:   updatedBoard.UpdatedAt,
		},
	); err != nil {
		s.logger.Error("cannot publish kanban board updated", slog.Any("error", err))
	}

	return s.modelToEntity(updatedBoard), nil
}

func (s *Service) Delete(ctx context.Context, id string) error {
	err := s.repo.Delete(ctx, id)
	if err != nil {
		s.logger.Error("cannot delete kanban board", slog.Any("error", err))
		return err
	}
	return nil
}
