package channels_members

import (
	"context"
	"github.com/coorpe-app/coorpe/libs/repositories/channels_members"
	"github.com/coorpe-app/coorpe/libs/repositories/channels_members/model"
	"github.com/google/uuid"
	"go.uber.org/fx"
)

type Params struct {
	fx.In

	Repo channels_members.Repository
}

func New(p Params) *Service {
	return &Service{
		repo: p.Repo,
	}
}

type Service struct {
	repo channels_members.Repository
}

type CreateInput struct {
	ChannelID string
	UserID    uuid.UUID
}

func (s *Service) GetMany(ctx context.Context, channelID string) ([]model.ChannelMember, error) {
	channelMembers, err := s.repo.GetMany(ctx, channelID)
	if err != nil {
		return nil, err
	}

	return channelMembers, nil
}
