package columns

import (
	"context"
	"github.com/coorpe-app/coorpe/internal/entity"
	"github.com/coorpe-app/coorpe/libs/bus"
	kanban_columns_bus "github.com/coorpe-app/coorpe/libs/bus/columns"
	"github.com/coorpe-app/coorpe/libs/logger"
	"github.com/coorpe-app/coorpe/libs/repositories/columns"
	"github.com/coorpe-app/coorpe/libs/repositories/columns/model"
	"github.com/coorpe-app/coorpe/pkg/typeid"
	"go.uber.org/fx"
	"log/slog"
)

type Params struct {
	fx.In

	Bus    *bus.Bus
	Logger logger.Logger
	Repo   columns.Repository
}

func New(p Params) *Service {
	return &Service{
		bus:    p.Bus,
		repo:   p.Repo,
		logger: p.Logger,
	}
}

type Service struct {
	bus    *bus.Bus
	repo   columns.Repository
	logger logger.Logger
}

func (s *Service) modelToEntity(column model.Column) entity.WorkspaceKanbanColumn {
	return entity.WorkspaceKanbanColumn{
		ID:       column.ID,
		Title:    column.Title,
		BoardID:  column.BoardID,
		Position: column.Position,
	}
}

func (s *Service) GetMany(ctx context.Context, boardID string) ([]entity.WorkspaceKanbanColumn, error) {
	dbColumns, err := s.repo.GetMany(ctx, boardID)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return nil, err
	}

	var entities []entity.WorkspaceKanbanColumn
	for _, column := range dbColumns {
		entities = append(entities, s.modelToEntity(column))
	}

	return entities, nil
}

func (s *Service) GetByID(ctx context.Context, id string) (entity.WorkspaceKanbanColumn, error) {
	column, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("err", slog.Any("error", err))
		return entity.WorkspaceKanbanColumnNil, err
	}

	return s.modelToEntity(column), nil
}

type CreateInput struct {
	Title    string
	BoardID  string
	Position int32
}

func (s *Service) Create(ctx context.Context, input CreateInput) (entity.WorkspaceKanbanColumn, error) {
	modelColumn := model.Column{
		ID:       typeid.GenerateID("column"),
		Title:    input.Title,
		BoardID:  input.BoardID,
		Position: input.Position,
	}

	createdColumn, err := s.repo.Create(ctx, modelColumn)
	if err != nil {
		s.logger.Error("cannot create kanban column", slog.Any("error", err))
		return entity.WorkspaceKanbanColumnNil, err
	}

	return s.modelToEntity(createdColumn), nil
}

type UpdateInput struct {
	Title    string
	Position int32
}

func (s *Service) Update(ctx context.Context, id string, input UpdateInput) (entity.WorkspaceKanbanColumn, error) {
	column, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("cannot get kanban column", slog.Any("error", err))
		return entity.WorkspaceKanbanColumnNil, err
	}

	column.Title = input.Title
	column.Position = input.Position

	updatedColumn, err := s.repo.Update(ctx, column)
	if err != nil {
		s.logger.Error("cannot update kanban column", slog.Any("error", err))
		return entity.WorkspaceKanbanColumnNil, err
	}

	if err := s.bus.Scrum.UpdateColumn.Publish(
		kanban_columns_bus.Column{
			Title:    updatedColumn.Title,
			Position: updatedColumn.Position,
		},
	); err != nil {
		s.logger.Error("cannot publish kanban column updated", slog.Any("error", err))
		return entity.WorkspaceKanbanColumnNil, err
	}

	return s.modelToEntity(updatedColumn), nil
}

func (s *Service) Delete(ctx context.Context, id string) error {
	err := s.repo.Delete(ctx, id)
	if err != nil {
		s.logger.Error("cannot delete kanban column", slog.Any("error", err))
		return err
	}

	return nil
}
